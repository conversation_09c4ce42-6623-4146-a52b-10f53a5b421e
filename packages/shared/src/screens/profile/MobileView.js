import {
  View,
  StyleSheet,
  ImageBackground,
  ScrollView,
  TouchableOpacity,
} from 'react-native'
import React from 'react'
import { Icon } from '@app-hero/native-icons'
import { useTranslation } from 'react-i18next'
import {
  Button,
  CheckBox,
  FilePicker,
  Loader,
  MobileInput,
  Text,
  TextInput,
} from '@libs/components'

import SearchDropDown from '@libs/components/src/DropDown/SearchDropdown'
import { useTheme } from '@libs/theme'
import ModalComponent from '../../components/modal'
import LoadingSpinner from '../../components/dynamicContentPanel/customLoader'

const MobileView = ({
  ctaEnable,
  handleChange,
  countryList,
  DropdownButton,
  dropdownRight,
  dropdownTop,
  dropdownWidth,
  successPopup,
  setSuccessPopup,
  isProfileUpdating,
  isLastNameAvailable,
  profileDetails,
  handleSave,
  getImageSource,
  setProfileDetails,
  toggleDropdown,
  setIsLastNameAvailable,
  setCTAEnable,
  navigation,
  mobileNumberError,
  firstNameError,
  lastNameError,
}) => {
  const { colors } = useTheme()
  const { t } = useTranslation()

  return (
    <ImageBackground
      // source={ProfileBg}
      style={{ flex: 1 }}
      imageStyle={{
        width: '60%',
        position: 'absolute',
        top: 0,
        left: '40%',
      }}
      // resizeMode="contain"
    >
      <ScrollView style={{}}>
        <View style={styles.mainContainer}>
          <View style={styles.profileContainer}>
            <View style={styles.profileLeftHeader}>
              <TouchableOpacity
                onPress={() => {
                  navigation.goBack()
                }}
              >
                <Icon
                  name="ArrowBack"
                  height={24}
                  width={24}
                  color={colors.black}
                />
              </TouchableOpacity>
              <Text style={[styles.myProfileText, { textAlign: 'center' }]}>
                {t('PROFILE.PERSONAL_INFO')}
              </Text>
            </View>

            <View style={styles.contentContainer}>
              <View
                style={{
                  flexDirection: 'column',
                  columnGap: 20,
                }}
              >
                <View style={styles.imageContainer}>
                  <View
                    style={{
                      height: '100%',
                      width: '100%',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      overflow: 'hidden',
                      paddingTop: 20,
                    }}
                  >
                    <FilePicker
                      isProfile
                      handleFileSelect={(image) =>
                        handleChange('profilePictureLocation', image)
                      }
                      profileStyle={{
                        width: 99,
                        height: 99,
                        borderRadius: 80,
                      }}
                      source={getImageSource()}
                    />
                  </View>
                </View>
                <View style={styles.formContainer}>
                  <View>
                    <TextInput
                      placeholder={t('TEXT_INPUT.FIRST_NAME')}
                      onChangeText={(text) => handleChange('firstName', text)}
                      value={profileDetails.firstName}
                      dropDownData=""
                      style={[styles.inputField, { marginBottom: 16 }]}
                      editable
                      limit={35}
                    />
                    {firstNameError && (
                      <Text
                        variant="display3"
                        color={colors.onAlert}
                        style={{ marginBottom: 16 }}
                      >
                        {firstNameError}
                      </Text>
                    )}
                    <TextInput
                      placeholder={t('TEXT_INPUT.LAST_NAME')}
                      onChangeText={(text) => handleChange('lastName', text)}
                      value={profileDetails.lastName}
                      style={[styles.inputField, { marginBottom: 16 }]}
                      dropDownData=""
                      editable={!isLastNameAvailable}
                      limit={35}
                    />
                    {lastNameError && (
                      <Text
                        variant="display3"
                        color={colors.onAlert}
                        style={{ marginBottom: 16 }}
                      >
                        {lastNameError}
                      </Text>
                    )}
                    <View
                      style={styles.checkboxContainer}
                      className="checkboxContainer"
                    >
                      <CheckBox
                        value={isLastNameAvailable}
                        handleCheck={() => {
                          setIsLastNameAvailable(!isLastNameAvailable)
                          setCTAEnable(true)
                        }}
                        checkboxColor={colors.white}
                      />
                      <Text variant="display4" color="#0A0A0A">
                        I don’t have last name
                      </Text>
                    </View>
                    <View style={styles.divider} />
                    <View>
                      <MobileInput
                        countryBasedOn={profileDetails.mobile.mobileCountry}
                        value={profileDetails.mobile.mobileNumber}
                        onChangeText={(text) =>
                          handleChange('mobile', {
                            dialCode: profileDetails.mobile.dialCode,
                            mobileNumber: text,
                            mobileCountryCode:
                              profileDetails.mobile.mobileCountryCode,
                            mobileCountry: profileDetails.mobile.mobileCountry,
                          })
                        }
                        placeholder={t('TEXT_INPUT.PHONE')}
                        dialCode={profileDetails.mobile.dialCode}
                        placeholderColor="#E0E0E0"
                        style={{
                          borderColor: '#E0E0E0',
                          paddingRight: 14,
                          fontSize: 16,
                          height: 56,
                        }}
                        handleCountrySelection={(selectedCountry) => {
                          setProfileDetails((previousData) => ({
                            ...previousData,
                            mobile: {
                              dialCode: selectedCountry.dial_code,
                              mobileNumber: previousData.mobile.mobileNumber,
                              mobileCountryCode: selectedCountry.code,
                              mobileCountry: selectedCountry.name.en,
                            },
                          }))
                          handleChange('mobile', {
                            dialCode: selectedCountry.dial_code,
                            mobileNumber: profileDetails.mobile.mobileNumber,
                            mobileCountryCode: selectedCountry.code,
                            mobileCountry: selectedCountry.name.en,
                          })
                        }}
                        code={profileDetails.mobile.mobileCountryCode}
                      />
                      {mobileNumberError && (
                        <Text
                          variant="display3"
                          color={colors.onAlert}
                          style={{ gap: 0 }}
                        >
                          {mobileNumberError}
                        </Text>
                      )}
                    </View>
                  </View>
                  <View
                    style={{ flex: 1, marginVertical: 20 }}
                    ref={DropdownButton}
                  >
                    <SearchDropDown
                      style={{
                        margin: 0,
                        width: '100%',
                        borderRadius: 4,
                        borderColor: '#E0E0E0',
                        height: 56,
                      }}
                      modalStyle={{ right: 22, height: 56 }}
                      label={t('TEXT_INPUT.COUNTRY')}
                      labelColor={colors.onNeutral}
                      placeHolderColor="#E0E0E0"
                      toggleDropdown={toggleDropdown}
                      onPress={(selectedCountry) =>
                        handleChange('country', selectedCountry)
                      }
                      dropdownWidth={dropdownWidth}
                      isCountryCode
                      items={countryList || []}
                      value={
                        profileDetails.country
                          ? {
                              Label: profileDetails.country,
                              Value: profileDetails.countryCode,
                            }
                          : {}
                      }
                      position={{
                        top: dropdownTop,
                        right: dropdownRight - 205,
                      }}
                      dropdownHeight={150}
                    />
                  </View>
                  <TextInput
                    label="Email ID"
                    onChangeText={() => {}}
                    value={profileDetails?.email || ''}
                    editable={false}
                    dropDownData=""
                    style={styles.inputField}
                    inputFieldStyle={{ color: colors.primaryPlaceHolder }}
                  />
                </View>
              </View>

              {isProfileUpdating ? (
                <View
                  style={{
                    marginBottom: 30,
                    marginTop: 16,
                    justifyContent: 'center',
                    flexDirection: 'row',
                  }}
                >
                  <LoadingSpinner size={32} hasFlexValue={false} />
                </View>
              ) : (
                <Button
                  label={t('BUTTON.SAVE_CHANGES')}
                  labelStyle={{
                    color: colors.white,
                    flexDirection: 'row',
                    fontSize: 14,
                    fontWeight: '700',
                  }}
                  onPress={() => handleSave()}
                  disable={!ctaEnable}
                  appearance="filled"
                  buttonStyle={{
                    alignItems: 'center',
                    borderRadius: 4,
                    paddingHorizontal: 80,
                    justifyContent: 'center',
                    backgroundColor: '#2563EB',
                    marginTop: 16,
                  }}
                />
              )}
            </View>
          </View>
        </View>
      </ScrollView>
      <ModalComponent
        visible={successPopup.visible}
        title={successPopup.message}
        buttonLabel={t('BUTTON.OK')}
        handleCloseModal={() => {
          setSuccessPopup({
            visible: false,
            message: '',
          })
        }}
        handleButton={() => {
          setSuccessPopup({
            visible: false,
            message: '',
          })
        }}
        buttonStyle={{
          borderRadius: 4,
        }}
        titleVariant="display3"
        labelStyle={{ fontWeight: 700 }}
        buttonColor="#2563EB"
        labelColors="white"
      />
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: '#F4F5FB',
  },
  profileContainer: {
    width: '100%',
  },
  profileLeftHeader: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 29,
  },
  myProfileText: {
    fontWeight: 700,
    fontSize: 18,
    color: '#1E1E2D',
    textTransform: 'uppercase',
    flex: 1,
    textAlign: 'center',
  },

  imageContainer: {
    height: 132,
    width: 155,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 13,
    backgroundColor: '#ECF3FF',
    marginHorizontal: 'auto',
  },

  formContainer: {
    paddingTop: 28,
  },

  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
    marginLeft: 2,
    marginTop: -10,
  },
  divider: {
    borderWidth: 0.4,
    borderColor: '#E0E0E0',
    borderStyle: 'solid',
    marginBottom: 24,
  },

  contentContainer: {
    width: '100%',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  inputField: {
    marginBottom: 16,
    paddingLeft: 24,
    paddingRight: 14,
    paddingVertical: 16,
    borderRadius: 4,
    height: 56,
    fontSize: 16,
    borderColor: '#E0E0E0',
  },
})

export default MobileView
