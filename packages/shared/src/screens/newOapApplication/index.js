import { Text } from 'react-native'
import React, { Suspense, useCallback, useRef } from 'react'
import { useIsFocused } from '@react-navigation/native'
import { useQuery } from '@tanstack/react-query'
import { ScreenLayout, SecureStore } from '@libs/utils'
import DesktopView from './DesktopView'
import MobileView from './MobileView'
import { generateOuthAccessToken, getOpportunitiesById } from '../../api'

const NewOapApplication = (props) => {
  const LayoutView = useCallback(
    ScreenLayout.withLayoutView(DesktopView, MobileView, MobileView),
    [],
  )

  const opportunityId = props.route.params.id

  const isFocused = useIsFocused()
  const scrollViewRef = useRef(null)

  const fetchOpportunities = async () => {
    let userProfile = await SecureStore.getItemAsync('userProfile')
    userProfile = JSON.parse(userProfile)

    const data = await getOpportunitiesById({
      opportunityId,
      email: userProfile?.email,
    })
    const finalData = data?.response?.[0]

    return finalData
  }

  const { data: opportunities, isFetching: isOpportunitiesFetching } = useQuery(
    {
      queryKey: [`getOpportunities-details-${opportunityId}`],
      queryFn: fetchOpportunities,
      enabled: isFocused && !!opportunityId,
      initialData: [],
    },
  )

  const { data: tokens } = useQuery({
    queryKey: ['generateOuthAccessToken', opportunities?.Id],
    queryFn: () =>
      generateOuthAccessToken(opportunities?.BusinessUnitFilter__c),
    enabled:
      isFocused &&
      !!opportunities?.Id &&
      !!opportunities?.BusinessUnitFilter__c &&
      !opportunities?.ApplicationSubmitted__c,
  })

  const viewProps = {
    opportunities,
    isOpportunitiesFetching,
    scrollViewRef,
    tokens,
  }

  return (
    <Suspense fallback={<Text>Loading</Text>}>
      <LayoutView {...viewProps} />
    </Suspense>
  )
}

export default NewOapApplication
