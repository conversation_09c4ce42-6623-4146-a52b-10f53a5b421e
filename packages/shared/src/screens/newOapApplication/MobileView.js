import React, { useState, useEffect } from 'react'
import { StyleSheet, TouchableOpacity, View, ScrollView } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { useTheme } from '@libs/theme'
import { Text, Button, DropDown, Loader } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { OapPortal } from '../../components'

const MobileView = ({
  details,
  showOap,
  setShowOap,
  formData,
  setFormData,
  formOptions,
  isSubmitting,
  handleFormSubmit,
  isOpportunitiesFetching,
}) => {
  const { colors } = useTheme()
  const navigation = useNavigation()
  const [submissionConfirmed, setSubmissionConfirmed] = useState(false)
  const [countdown, setCountdown] = useState(30)

  // Auto-redirect countdown when submission is confirmed
  useEffect(() => {
    let interval = null
    if (submissionConfirmed) {
      interval = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            // Redirect to my-application screen
            navigation.navigate('my-application')
            return 0
          }
          return prevCountdown - 1
        })
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [submissionConfirmed, navigation])

  if (isOpportunitiesFetching) {
    return (
      <View style={styles.loadingContainer}>
        <Loader />
        <Text variant="heading4" style={{ marginTop: 16 }}>
          Loading application...
        </Text>
      </View>
    )
  }

  const handleFieldChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const isFormValid = formData.programme && formData.campus && formData.intake

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="ArrowLeft" size={20} color={colors.onNeutral} />
          <Text variant="display3" style={{ marginLeft: 8 }}>
            BACK TO APPLICATIONS
          </Text>
        </TouchableOpacity>
      </View>

      {/* Program Title */}
      <View style={styles.programHeader}>
        <Text variant="heading5" color={colors.white} numberOfLines={2}>
          &lt;Msc Global Human Resources Management&gt; - &lt;University
          name&gt;
        </Text>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressSection}>
        <View style={styles.brandContainer}>
          <View style={styles.brandLogo}>
            <Text variant="heading6" color={colors.white}>
              BSBI
            </Text>
          </View>
          <View style={styles.progressContainer}>
            <Text
              variant="caption"
              color={colors.white}
              style={{ marginBottom: 8 }}
            >
              19% completed
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '19%' }]} />
            </View>
          </View>
        </View>
      </View>

      {/* Form Content */}
      <ScrollView style={styles.formContainer}>
        <View style={styles.formContent}>
          <Text variant="heading4" style={styles.formTitle}>
            Program Information
          </Text>

          {/* Programme Field */}
          <View style={styles.fieldContainer}>
            <Text variant="display3" style={styles.fieldLabel}>
              Please confirm your programme and intake date{' '}
              <Text style={styles.required}>*</Text>
            </Text>
            <DropDown
              items={formOptions.programmes}
              value={formData.programme}
              onPress={(item) => handleFieldChange('programme', item)}
              style={styles.dropdown}
              placeholder="MSc Global Human Resources Management"
            />
            {formData.programme && (
              <Icon
                name="Check"
                size={16}
                color={colors.success}
                style={styles.fieldIcon}
              />
            )}
          </View>

          {/* Campus Field */}
          <View style={styles.fieldContainer}>
            <Text variant="display3" style={styles.fieldLabel}>
              Campus <Text style={styles.required}>*</Text>
            </Text>
            <DropDown
              items={formOptions.campuses}
              value={formData.campus}
              onPress={(item) => handleFieldChange('campus', item)}
              style={styles.dropdown}
              placeholder="Berlin"
            />
            {formData.campus && (
              <Icon
                name="Check"
                size={16}
                color={colors.success}
                style={styles.fieldIcon}
              />
            )}
          </View>

          {/* Intake Field */}
          <View style={styles.fieldContainer}>
            <Text variant="display3" style={styles.fieldLabel}>
              Intake <Text style={styles.required}>*</Text>
            </Text>
            <DropDown
              items={formOptions.intakes}
              value={formData.intake}
              onPress={(item) => handleFieldChange('intake', item)}
              style={styles.dropdown}
              placeholder="Select"
            />
          </View>

          {/* Next Button */}
          <View style={styles.buttonContainer}>
            {isSubmitting ? (
              <View style={styles.loadingButton}>
                <Loader size={20} hasFlexValue={false} />
                <Text variant="display3" style={{ marginLeft: 8 }}>
                  Processing...
                </Text>
              </View>
            ) : (
              <Button
                label="Next"
                buttonColor={colors.primary}
                labelColors={colors.white}
                buttonStyle={[
                  styles.nextButton,
                  !isFormValid && styles.disabledButton,
                ]}
                disable={!isFormValid}
                onPress={handleFormSubmit}
              />
            )}
          </View>

          {/* Language Selector */}
          <View style={styles.languageSelector}>
            <Icon name="Globe" size={16} color={colors.onAlert} />
            <Text
              variant="caption"
              color={colors.onNeutral}
              style={{ marginLeft: 8 }}
            >
              English
            </Text>
          </View>
        </View>
      </ScrollView>

      <OapPortal
        visible={showOap}
        handleClose={() => {
          setShowOap(false)
        }}
        portalUrl="https://hzu-directoap-dev.apphero.io"
        redirectPath="/application-filter"
        token={details?.token}
        handleSubmissionConfirmation={(data) => {
          if (data === true) {
            setSubmissionConfirmed(true)
            setCountdown(30) // Reset countdown when submission is confirmed
          }
        }}
      />
      {submissionConfirmed && (
        <View style={styles.confirmationMessage}>
          <Text
            variant="display4"
            color={colors.neutral}
            style={{ textAlign: 'center', marginBottom: 8 }}
          >
            You will redirected to My application page in{' '}
            <Text style={{ color: colors.primary, fontWeight: '600' }}>
              {countdown} sec
            </Text>
          </Text>
          <Text
            variant="caption"
            color={colors.neutral}
            style={{ textAlign: 'center', marginBottom: 16 }}
          >
            or
          </Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('my-application')}
            style={styles.redirectLink}
          >
            <Text
              variant="display4"
              color={colors.primary}
              style={{ textAlign: 'center', textDecorationLine: 'underline' }}
            >
              Click here to go to My application page →
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  programHeader: {
    backgroundColor: '#2C3E50',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  progressSection: {
    backgroundColor: '#4A90E2',
    paddingVertical: 16,
  },
  brandContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  brandLogo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 16,
  },
  progressContainer: {
    flex: 1,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  formContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  formContent: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  formTitle: {
    marginBottom: 24,
    color: '#2C3E50',
  },
  fieldContainer: {
    marginBottom: 20,
    position: 'relative',
  },
  fieldLabel: {
    marginBottom: 8,
    color: '#2C3E50',
    fontSize: 14,
  },
  required: {
    color: '#D72C2C',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  fieldIcon: {
    position: 'absolute',
    right: 12,
    top: 36,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 16,
  },
  nextButton: {
    width: '100%',
    paddingVertical: 14,
    borderRadius: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  confirmationMessage: {
    position: 'absolute',
    bottom: 80,
    left: 16,
    right: 16,
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  redirectLink: {
    padding: 8,
  },
})

export default MobileView
