import React from 'react'
import {
  View,
  StyleSheet,
  ScrollView,
  useWindowDimensions,
  TouchableOpacity,
  Image,
} from 'react-native'
import { HelpAndSupportConfirmation } from '@apphero/assets'
import { useTranslation } from 'react-i18next'
import {
  Button,
  DropDown,
  Text,
  TextInput,
  MobileInput,
} from '@libs/components'
import { useTheme } from '@libs/theme'
import { TitleHeader } from '../../components/headerTitle'
import { useHelpAndSupport, MetadataField } from './helpAndSupportUtils'

const MobileView = ({ userProfile }) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const { width } = useWindowDimensions()

  // Use the common hook
  const {
    formData,
    errors,
    isValid,
    isSubmitting,
    isSubmitted,
    subjectDropdownRef,
    applicationDropdownRef,
    dropdownState,
    subjectCategories,
    applications,
    toggleSubjectDropdown,
    toggleApplicationDropdown,
    handleChange,
    handleSubmit,
    navigation,
  } = useHelpAndSupport(userProfile)

  const dropdownHeight = applications?.length ? applications.length * 40 : 200

  return (
    <ScrollView
      contentContainerStyle={styles.mainContainer(width)}
      style={{ backgroundColor: colors.backgroundSecondary }}
    >
      <View
        style={{
          paddingTop: 16,
          paddingBottom: 32,
        }}
      >
        <TitleHeader title={t('HELP_SUPPORT.TITLE')} />
      </View>

      <View style={styles.contentContainer(width)}>
        {isSubmitted ? (
          <View style={styles.thankYouContainer}>
            <Image
              source={HelpAndSupportConfirmation}
              style={{
                height: 50,
                width: 50,
              }}
            />
            <Text variant="display1" style={styles.thankYouTitle}>
              {t('HELP_SUPPORT.THANK_YOU')}
            </Text>
            <Text variant="display4" style={styles.thankYouMessage}>
              {t('HELP_SUPPORT.SUBMISSION_RECEIVED')}
            </Text>
            <Button
              label="Take me back to Dashboard"
              buttonColor={colors.primary}
              labelColors={colors.white}
              onPress={() => {
                // Force a refresh of the dashboard data by using reset instead of navigate
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'dashboard' }],
                })
              }}
              buttonStyle={styles.backButton}
              labelStyle={{ fontWeight: 700, fontSize: 16 }}
            />
          </View>
        ) : (
          <View style={styles.formContainer}>
            <View style={styles.formHeader}>
              <Text variant="display1" style={styles.formTitle}>
                {t('HELP_SUPPORT.QUOTES')}
              </Text>
              <Text variant="display4" style={styles.formSubtitle}>
                {t('HELP_SUPPORT.FILL_FORM')}
              </Text>
            </View>

            <View style={styles.formFields}>
              {/* Full Name */}
              <View style={styles.fieldContainer}>
                <TextInput
                  value={formData.fullName}
                  onChangeText={(text) => handleChange('fullName', text)}
                  placeholder={t('HELP_SUPPORT.FULL_NAME')}
                  style={styles.inputField}
                  iconName="User2"
                  limit={70}
                  iconSize={20}
                  inputFieldStyle={{ marginLeft: 12 }}
                />
              </View>

              {/* Email */}
              <View style={styles.fieldContainer}>
                <TextInput
                  value={formData.email}
                  onChangeText={(text) => handleChange('email', text)}
                  placeholder={t('HELP_SUPPORT.EMAIL')}
                  style={[
                    styles.inputField,
                    errors.email
                      ? { borderColor: colors.error }
                      : {
                          borderColor: 'rgba(22, 36, 71, 0.2)',
                          borderWidth: 1,
                          borderRadius: 8,
                          opacity: 0.4,
                        },
                  ]}
                  iconName="Mail2"
                  iconSize={20}
                  inputFieldStyle={{ marginLeft: 12 }}
                  editable={false}
                />
                {errors.email && (
                  <Text style={styles.errorText}>{errors.email}</Text>
                )}
              </View>

              {/* Phone Number */}
              <View style={styles.fieldContainer}>
                <MobileInput
                  value={formData.phoneNumber}
                  onChangeText={(text) => handleChange('phoneNumber', text)}
                  placeholder={t('HELP_SUPPORT.PHONE')}
                  style={styles.mobileInput}
                  handleCountrySelection={(selectedCountry) =>
                    handleChange('countryCode', selectedCountry.dial_code)
                  }
                  code={formData.countryCode}
                />
                {errors.phoneNumber && (
                  <Text style={styles.errorText}>{errors.phoneNumber}</Text>
                )}
              </View>

              {/* Subject/Category */}
              <View style={styles.fieldContainer}>
                <View ref={subjectDropdownRef} style={{ marginTop: 12 }}>
                  <DropDown
                    items={subjectCategories}
                    toggleDropdown={toggleSubjectDropdown}
                    position={{
                      left: dropdownState.subjectLeft,
                      top: dropdownState.subjectTop + 60,
                    }}
                    dropdownWidth={dropdownState.dropdownWidth}
                    onPress={(selectedOption) => {
                      handleChange('subject', selectedOption)
                    }}
                    haveUnderLine
                    label={t('HELP_SUPPORT.SELECT_SUBJECT')}
                    dropdownHeight={125}
                    style={styles.dropdownStyle}
                    labelStyle={{ padding: 100 }}
                    placeHolderColor="#16244733"
                    value={formData.subject}
                  />
                </View>
              </View>

              {/* Application Selector */}

              <View style={styles.fieldContainer}>
                <View ref={applicationDropdownRef} style={{ marginTop: 12 }}>
                  <DropDown
                    items={applications}
                    toggleDropdown={toggleApplicationDropdown}
                    position={{
                      left: dropdownState.applicationLeft,
                      top: dropdownState.applicationTop + 70,
                    }}
                    dropdownWidth={dropdownState.dropdownWidth}
                    onPress={(selectedOption) => {
                      handleChange('application', selectedOption)
                    }}
                    haveUnderLine
                    label={t('HELP_SUPPORT.SELECT_APPLICATION')}
                    dropdownHeight={Math.min(dropdownHeight, 200)}
                    style={{
                      ...styles.dropdownStyle,
                      ...{ marginVertical: 5 },
                    }}
                    labelStyle={{ padding: 100 }}
                    placeHolderColor="#16244733"
                    value={formData.application}
                  />
                </View>
              </View>

              {/* Message */}
              <View style={[styles.messageContainer, styles.fieldContainer]}>
                <MetadataField
                  onChange={(text) => handleChange('message', text)}
                  placeholder={t('HELP_SUPPORT.ENTER_MESSAGE')}
                  multiline
                  style={{
                    ...styles.messageInput,
                    ...{ border: '1px solid', paddingHorizontal: 15 },
                  }}
                  placeHolderColor="#16244733"
                  value={formData.message}
                  colors={colors}
                />
              </View>

              {/* Submit Button */}

              <View style={styles.buttonContainer(width)}>
                <TouchableOpacity
                  style={styles.submitButton(colors, !isValid || isSubmitting)}
                  onPress={handleSubmit}
                  disabled={!isValid || isSubmitting}
                >
                  <Text variant="display3" style={styles.submitButtonText}>
                    {isSubmitting
                      ? t('HELP_SUPPORT.SENDING')
                      : t('HELP_SUPPORT.SEND_MESSAGE')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  mainContainer: () => ({
    paddingHorizontal: 24,
    paddingVertical: 24,
    flex: 1,
  }),
  contentContainer: () => ({
    borderRadius: 12,
    width: '100%',
    flex: 1,
  }),
  formContainer: {
    width: '100%',
  },
  formHeader: {
    marginBottom: 8,
    paddingBottom: 16,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 700,
    marginBottom: 12,
  },
  fieldContainer: {
    marginBottom: 0,
  },
  inputContainer: () => ({
    borderWidth: 1,
    borderRadius: 4,
    overflow: 'hidden',
    padding: 6,
  }),
  metadataContainer: {
    marginTop: 8,
  },
  label: (colors) => ({
    marginBottom: 8,
    fontWeight: '400',
    color: colors.textSecondary,
    fontSize: 14,
  }),

  formSubtitle: {
    color: '#666',
    marginBottom: 4,
  },
  formFields: {
    width: '100%',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
  },
  inputField: {
    height: 56,
    marginBottom: 16,
    borderRadius: 8,
    borderColor: 'rgba(22, 36, 71, 0.2)',
  },
  mobileInput: {
    height: 56,
    borderRadius: 8,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    padding: 0,
    marginBottom: 4,
  },
  dropdownStyle: {
    height: 56,
    borderRadius: 12,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    margin: 0,
  },
  messageContainer: {
    marginTop: 12,
    paddingRight: 18,
  },
  messageInput: {
    height: 120,
    marginBottom: 24,
    paddingLeft: 14,
    paddingTop: 14,
    borderRadius: 8,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    textAlignVertical: 'top',
    fontSize: 14,
  },
  buttonContainer: () => ({
    width: '100%',
  }),
  submitButton: (colors, canDisable) => ({
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 50,
    borderRadius: 4,
    alignItems: 'center',
    opacity: canDisable ? 0.5 : 1,
  }),
  submitButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  thankYouContainer: {
    alignItems: 'center',
    justifyContent: 'start',
    padding: 20,
    flex: 1,
    marginTop: 40,
  },
  thankYouTitle: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
    marginTop: 24,
    marginBottom: 12,
    textAlign: 'center',
  },
  thankYouMessage: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    maxWidth: 400,
  },
  backButton: {
    width: '100%',
    borderRadius: 8,
    height: 56,
  },
})

export default MobileView
