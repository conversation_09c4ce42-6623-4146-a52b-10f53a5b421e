import React, { useState, useRef, useEffect } from 'react'
import { useNavigation, useIsFocused } from '@react-navigation/native'
import { useQuery } from '@tanstack/react-query'
import {
  createCaseForHelpAndSupport,
  getLookUpData,
  getOpportunities,
} from '../../api'
import { validateForm } from '../../utils/validation/helpAndSupportValidation'

// Custom hook for Help and Support functionality
export const useHelpAndSupport = (userProfile) => {
  const isFocused = useIsFocused()
  const navigation = useNavigation()

  // Form state
  const [formData, setFormData] = useState({
    fullName: userProfile?.firstName
      ? `${userProfile.firstName} ${userProfile.lastName || ''}`.trim()
      : '',
    email: userProfile?.email || '',
    phoneNumber: userProfile?.mobileNumber || '',
    countryCode: userProfile?.countryCode || '+375',
    subject: {},
    application: {},
    brand: {},
    message: '',
  })

  const [errors, setErrors] = useState({})
  const [isValid, setIsValid] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  // Reset form on initial load
  useEffect(() => {
    setFormData({
      fullName: userProfile?.firstName
        ? `${userProfile.firstName} ${userProfile.lastName || ''}`.trim()
        : '',
      email: userProfile?.email || '',
      phoneNumber: userProfile?.phoneNumber?.split('-')[1] || '',
      countryCode: userProfile?.phoneNumber?.split('-')[0] || '+375',
      subject: {},
      application: {},
      brand: {},
      message: '',
    })
    setErrors({})
    setIsValid(false)
    setIsSubmitted(false)
    setIsSubmitting(false)
  }, [isFocused])

  // Refs for dropdowns
  const subjectDropdownRef = useRef(null)
  const applicationDropdownRef = useRef(null)
  const brandDropdownRef = useRef(null)
  const [dropdownState, setDropdownState] = useState({
    subjectTop: 0,
    subjectLeft: 0,
    applicationTop: 0,
    applicationLeft: 0,
    brandTop: 0,
    brandLeft: 0,
    dropdownWidth: 0,
  })

  // Fetch supported brands
  const { data: supportedBrands } = useQuery({
    queryKey: ['SUPPORTED_BRANDS'],
    queryFn: async () => {
      const response = await getLookUpData('Brand', 'SUPPORTED_BRANDS')
      return response?.Item.options
    },
    enabled: true,
  })

  // Helper function to filter options based on current path
  const hideWhen = (options, currentPath) =>
    options?.filter(
      (option) => !option.hideWhen || option.hideWhen !== currentPath,
    )

  // Fetch subject categories
  const { data: subjectCategories } = useQuery({
    queryKey: ['getSubjectCategories'],
    queryFn: async () => {
      const response = await getLookUpData('HELP_AND_SUPPORT', 'TYPE')
      return response?.Item.options
        ? hideWhen(response?.Item.options, window.location.pathname) || []
        : []
    },
    enabled: true,
  })

  // Fetch applications
  const fetchAllOpportunities = async () => {
    const response = await getOpportunities({ email: userProfile?.email })

    return response?.response
      ? response?.response?.map((opportunity) => ({
          label: [
            opportunity.ProgrammeName__c,
            opportunity.Institution_Full_Name__c,
            opportunity.Delivery_Mode__c,
            opportunity.location,
            opportunity.Product_Intake_Date__c
              ? new Date(opportunity.Product_Intake_Date__c)
                  .toLocaleDateString('en-US', {
                    month: 'short',
                    year: 'numeric',
                  })
                  .split(' ')
                  .join('-')
              : new Date(
                  opportunity.OpportunityLineItems?.records?.[0]?.Intake_Date__c,
                )
                  .toLocaleDateString('en-US', {
                    month: 'short',
                    year: 'numeric',
                  })
                  .split(' ')
                  .join('-') ?? '',
          ]
            .filter((val) => val)
            .join(', '),
          value: opportunity?.Id,
          brand: {
            label: opportunity?.Institution,
            value: opportunity?.BusinessUnitFilter__c,
          },
        }))
      : []
  }

  const { data: applications } = useQuery({
    queryKey: [`fetch-opp-${formData?.email}`],
    queryFn: fetchAllOpportunities,
    enabled: isFocused && !!userProfile?.email,
  })

  // Dropdown toggle functions
  const toggleSubjectDropdown = () => {
    if (subjectDropdownRef.current) {
      subjectDropdownRef.current.measure(
        (_, __, dropdownWidth, ___, px, py) => {
          setDropdownState({
            ...dropdownState,
            subjectTop: py,
            subjectLeft: px,
            dropdownWidth,
          })
        },
      )
    }
  }

  const toggleApplicationDropdown = () => {
    if (applicationDropdownRef.current) {
      applicationDropdownRef.current.measure(
        (_, __, dropdownWidth, ___, px, py) => {
          setDropdownState({
            ...dropdownState,
            applicationTop: py,
            applicationLeft: px,
            dropdownWidth,
          })
        },
      )
    }
  }

  const toggleBrandDropdown = () => {
    if (brandDropdownRef.current) {
      brandDropdownRef.current.measure((_, __, dropdownWidth, ___, px, py) => {
        setDropdownState({
          ...dropdownState,
          brandTop: py,
          brandLeft: px,
          dropdownWidth,
        })
      })
    }
  }

  // Form change handler
  const handleChange = (field, value) => {
    const newFormData = {
      ...formData,
      [field]: value,
    }
    if (field === 'application') {
      newFormData.brand = value?.brand
    }
    setFormData(newFormData)

    // Validate the form after each change
    const { isValid: isValidOnChange, errors: validationErrors } =
      validateForm(newFormData)

    setErrors(validationErrors)
    setIsValid(isValidOnChange)
  }

  // Form submission handler
  const handleSubmit = async () => {
    // Validate the form before submission
    const { errors: validationErrors, isValid: isValidOnSubmit } =
      validateForm(formData)

    // Preserve the unauth error if it exists
    const unauthError = errors.unauth ? { unauth: errors.unauth } : {}

    // Update errors state with validation results while preserving unauth error
    setErrors({
      ...validationErrors,
      ...unauthError,
    })

    setIsValid(isValidOnSubmit)

    if (!isValidOnSubmit) return

    setIsSubmitting(true)

    const currentDate = new Date().toLocaleDateString('en-GB')

    try {
      // Prepare the payload for the API
      const payload = {
        fullName: formData.fullName,
        contactEmailId: formData.email,
        phoneNumber: `${formData.countryCode}${formData.phoneNumber}`,
        type:
          typeof formData.subject === 'object'
            ? formData.subject.value || ''
            : '',
        description: formData.message,
        brand:
          typeof formData.brand === 'object' ? formData.brand.value || '' : '',
        opportunityId: formData.application?.value || '',
        subject: `Case created from App Hero website - ${currentDate}`,
      }

      // Call the API
      const response = await createCaseForHelpAndSupport(payload)
      // Check if the response is successful
      if (response?.data?.success) {
        setIsSubmitting(false)
        setIsSubmitted(true)
      } else if (response.error) {
        setIsSubmitting(false)
        setErrors({
          unauth:
            'No application found here. Please check if this is the same email you used for your application.',
        })
      } else {
        setIsSubmitting(false)
      }
    } catch (error) {
      setIsSubmitting(false)
      // Set a generic error message
      setErrors((prev) => ({
        ...prev,
        unauth:
          'An error occurred while submitting your request. Please try again later.',
      }))
    }
  }

  return {
    formData,
    errors,
    isValid,
    isSubmitting,
    isSubmitted,
    subjectDropdownRef,
    applicationDropdownRef,
    brandDropdownRef,
    dropdownState,
    supportedBrands,
    subjectCategories,
    applications,
    toggleSubjectDropdown,
    toggleApplicationDropdown,
    toggleBrandDropdown,
    handleChange,
    handleSubmit,
    navigation,
  }
}

// MetadataField component for textarea inputs
export const MetadataField = ({
  label,
  value,
  onChange,
  multiline = false,
  required = false,
  placeholder,
  colors,
  style,
  placeHolderColor,
}) => {
  // Generate a unique ID for this instance
  const inputId = React.useMemo(
    () => `stylemeta-${Math.random().toString(36).substring(2, 11)}`,
    [],
  )

  useEffect(() => {
    // Remove any existing style element for this input
    const styleId = `${inputId}-style`
    const existingStyle = document.getElementById(styleId)
    if (existingStyle) {
      existingStyle.remove()
    }

    // Create a new style element with the current placeholder color
    const styleElement = document.createElement('style')
    styleElement.id = styleId
    styleElement.innerHTML = `
      #${inputId}::placeholder {
        color: ${placeHolderColor || '#A1A1AA'} !important;
      }
      input#${inputId}::placeholder {
        color: ${placeHolderColor || '#A1A1AA'} !important;
      }
      textarea#${inputId}::placeholder {
        color: ${placeHolderColor || '#A1A1AA'} !important;
      }
    `
    document.head.appendChild(styleElement)

    // Cleanup function to remove the style element when component unmounts
    return () => {
      const styleToRemove = document.getElementById(styleId)
      if (styleToRemove) {
        styleToRemove.remove()
      }
    }
  }, [inputId, placeHolderColor])

  const commonProps = {
    value,
    onChange: (e) => onChange(e.target.value),
    placeholder: placeholder || `Enter ${label?.toLowerCase() || ''}`,
    required,
    id: inputId,
    style: {
      width: '100%',
      border: 'none',
      outline: 'none',
      fontSize: '14px',
      color: colors?.textPrimary,
      minHeight: multiline ? '80px' : 'auto',
      resize: 'vertical',
      fontFamily: 'Inter',
      backgroundColor: 'transparent',
      '::placeholder': {
        color: placeHolderColor || '#A1A1AA',
      },
      ...style,
    },
  }

  return multiline ? (
    <textarea {...commonProps} />
  ) : (
    <input type="text" {...commonProps} />
  )
}
