import React, { useCallback, Suspense } from 'react'
import { ScreenLayout } from '@libs/utils'
import { Text } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { useAtom } from 'jotai'
import { userProfile } from '../../utils/atom'
import DesktopView from './DesktopView'
import MobileView from './MobileView'

const HelpAndSupport = () => {
  const navigation = useNavigation()
  const [userprofile] = useAtom(userProfile)

  const LayoutView = useCallback(
    ScreenLayout.withLayoutView(DesktopView, MobileView, MobileView),
    [],
  )

  const viewProps = {
    navigation,
    userProfile: userprofile,
  }

  return (
    <Suspense fallback={<Text>Loading</Text>}>
      <LayoutView {...viewProps} />
    </Suspense>
  )
}

export default HelpAndSupport
