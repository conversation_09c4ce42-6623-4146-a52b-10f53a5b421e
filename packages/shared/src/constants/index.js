import { badgeColor } from './badgeColor'
import {
  languageDropDown,
  menuItems,
  profileDrawerMenu,
  profileDrawerTabs,
  MobileMenuItems,
  loadingMessage,
  profileDrawerTabsWithoutPathWay,
} from './dropDownData'
import { excludedDurationBrands } from './durationRemovalBrand'
import { supportSections } from './supportSection'

export {
  languageDropDown,
  menuItems,
  profileDrawerMenu,
  profileDrawerTabs,
  MobileMenuItems,
  loadingMessage,
  profileDrawerTabsWithoutPathWay,
  excludedDurationBrands,
  badgeColor,
  supportSections,
}
