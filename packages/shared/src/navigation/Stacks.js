import { useAtom } from 'jotai'
import React, { useEffect, useState } from 'react'
import { t } from 'i18next'
import { isMobile, isTablet } from '@libs/utils/src/screenLayout'
import { useNavigation } from '@react-navigation/native'
import { Linking, useWindowDimensions } from 'react-native'
import queryString from 'query-string'
import Auth from '../screens/auth'
import MyApplication from '../screens/myApplication'
import CompareProgram from '../screens/compareProgrammes'
import ViewApplicationDetails from '../screens/viewApplicationDetails'
import Profile from '../screens/profile'
import ProgrammeDetails from '../screens/programmeDetails'
import ProgrammeApplication from '../screens/programmeApplication'
import ApplicationBasket from '../screens/applicationBasket'
import ChangePassword from '../screens/changePassword'
import ChangeProfilePicture from '../screens/changeProfilePicture'
import StudentDetail from '../screens/studentDetail'
import Notification from '../screens/notification'
import ProtectedRoute from './ProtectedRoute'
import PathFinder from '../screens/pathFinder'
import DashBoard from '../screens/dashboard'
import ActionRequired from '../screens/actionRequired'
import LinkedInError from '../screens/linkedin-error'
import HelpAndSupport from '../screens/helpAndSupport'
import NewOapApplication from '../screens/newOapApplication'

export const Stacks = (Stack) => {
  const { width } = useWindowDimensions()
  const mobile = isMobile(width)
  const tablet = isTablet(width)
  const [initialParams, setInitialParams] = useState({})

  useEffect(() => {
    const handleInitialURL = async () => {
      const url = await Linking.getInitialURL()

      if (url) {
        const queryParams = queryString.parseUrl(url)
        setInitialParams(queryParams.query)
      }
    }
    handleInitialURL()
  }, [])

  return (
    <Stack.Group
      initialRouteName="login"
      screenOptions={{
        title: '',
      }}
    >
      <Stack.Screen name="linkedin-loader" options={{ headerShown: false }}>
        {(screenProps) => <LinkedInError {...screenProps} {...initialParams} />}
      </Stack.Screen>
      <Stack.Screen name="login" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} {...initialParams} />}
      </Stack.Screen>
      <Stack.Screen name="sign-up" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} {...initialParams} />}
      </Stack.Screen>
      <Stack.Screen name="set-new-password" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} />}
      </Stack.Screen>
      <Stack.Screen name="forgot-password" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} />}
      </Stack.Screen>
      <Stack.Screen name="set-password" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} />}
      </Stack.Screen>
      <Stack.Screen name="otp" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} />}
      </Stack.Screen>
      <Stack.Screen name="privacy-policy" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} />}
      </Stack.Screen>
      <Stack.Screen name="auth-help-support" options={{ headerShown: false }}>
        {(screenProps) => <Auth {...screenProps} />}
      </Stack.Screen>
      <Stack.Screen name="path-finder">
        {(screenProps) => (
          <ProtectedRoute>
            <PathFinder {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="my-application">
        {(screenProps) => (
          <ProtectedRoute>
            <MyApplication {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen
        name="notification"
        options={mobile || tablet ? { headerShown: false } : ''}
      >
        {(screenProps) => (
          <ProtectedRoute>
            <Notification {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="compare-programmes">
        {(screenProps) => (
          <ProtectedRoute>
            <CompareProgram {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="view-application-details">
        {(screenProps) => (
          <ProtectedRoute>
            <ViewApplicationDetails {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>

      <Stack.Screen
        name="profile"
        options={mobile || tablet ? { headerShown: false } : ''}
      >
        {(screenProps) => (
          <ProtectedRoute>
            <Profile {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="programme-detail">
        {(screenProps) => (
          <ProtectedRoute>
            <ProgrammeDetails {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="apply-programmes" options={{ headerShown: false }}>
        {(screenProps) => (
          <ProtectedRoute>
            <ProgrammeApplication {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="student-details">
        {(screenProps) => (
          <ProtectedRoute>
            <StudentDetail {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="application-basket">
        {(screenProps) => (
          <ProtectedRoute>
            <ApplicationBasket {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen
        name="change-password"
        options={mobile || tablet ? { headerShown: false } : ''}
      >
        {(screenProps) => (
          <ProtectedRoute>
            <ChangePassword {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="profile-picture">
        {(screenProps) => (
          <ProtectedRoute>
            <ChangeProfilePicture {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>

      <Stack.Screen name="dashboard">
        {(screenProps) => (
          <ProtectedRoute>
            <DashBoard
              {...screenProps}
              {...initialParams}
              setInitialParams={setInitialParams}
            />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="action-required">
        {(screenProps) => (
          <ProtectedRoute>
            <ActionRequired {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="help-and-support">
        {(screenProps) => (
          <ProtectedRoute>
            <HelpAndSupport {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="new-oap-application">
        {(screenProps) => (
          <ProtectedRoute>
            <NewOapApplication {...screenProps} />
          </ProtectedRoute>
        )}
      </Stack.Screen>
    </Stack.Group>
  )
}
