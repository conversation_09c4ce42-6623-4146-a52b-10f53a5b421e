/**
 * Utility functions for handling cookies
 */

/**
 * Sets a cookie with the given name, value, and options
 * @param {string} name - The name of the cookie
 * @param {string} value - The value of the cookie
 * @param {Object} options - Cookie options
 * @param {number} options.days - Number of days until the cookie expires
 * @param {string} options.path - The path for the cookie
 * @param {string} options.domain - The domain for the cookie
 * @param {boolean} options.secure - Whether the cookie should be secure
 * @param {boolean} options.httpOnly - Whether the cookie should be HTTP only
 * @param {string} options.sameSite - SameSite attribute for the cookie (None, Lax, Strict)
 */
export const setCookie = (name, value, options = {}) => {
  const {
    days = 7,
    path = '/',
    domain = '',
    secure = true,
    httpOnly = false,
    sameSite = 'Lax',
  } = options

  // Calculate expiration date
  const expirationDate = new Date()
  expirationDate.setDate(expirationDate.getDate() + days)

  // Build cookie string
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`
  cookieString += `; expires=${expirationDate.toUTCString()}`
  cookieString += `; path=${path}`

  if (domain) {
    cookieString += `; domain=${domain}`
  }

  if (secure) {
    cookieString += '; secure'
  }

  if (httpOnly) {
    cookieString += '; httpOnly'
  }

  cookieString += `; sameSite=${sameSite}`

  console.log('cookieString', cookieString)

  // Set the cookie
  document.cookie = cookieString
}

/**
 * Gets a cookie by name
 * @param {string} name - The name of the cookie to get
 * @returns {string|null} The cookie value or null if not found
 */
export const getCookie = (name) => {
  const cookies = document.cookie.split(';')
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim()
    // Check if this cookie starts with the name we're looking for
    if (cookie.startsWith(`${encodeURIComponent(name)}=`)) {
      return decodeURIComponent(cookie.substring(name.length + 1))
    }
  }
  return null
}

/**
 * Deletes a cookie by name
 * @param {string} name - The name of the cookie to delete
 * @param {Object} options - Cookie options
 * @param {string} options.path - The path for the cookie
 * @param {string} options.domain - The domain for the cookie
 */
export const deleteCookie = (name, options = {}) => {
  const { path = '/', domain = '' } = options

  // Set expiration date to the past
  let cookieString = `${encodeURIComponent(
    name,
  )}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`

  if (domain) {
    cookieString += `; domain=${domain}`
  }

  document.cookie = cookieString
}

/**
 * Sets authentication tokens in cookies
 * @param {Object} tokens - The authentication tokens
 * @param {string} tokens.accessToken - The access token
 * @param {string} tokens.idToken - The ID token
 * @param {string} tokens.refreshToken - The refresh token
 * @param {Object} options - Cookie options
 */
export const setAuthCookies = (tokens, options = {}) => {
  if (!tokens) return

  const { accessToken, idToken, refreshToken } = tokens

  if (accessToken) {
    setCookie('access_token', accessToken, options)
  }

  if (idToken) {
    setCookie('id_token', idToken, options)
  }

  if (refreshToken) {
    // Refresh token typically needs longer expiration
    const refreshOptions = { ...options, days: 30 }
    setCookie('refresh_token', refreshToken, refreshOptions)
  }
}

/**
 * Gets all authentication tokens from cookies
 * @returns {Object} The authentication tokens
 */
export const getAuthCookies = () => {
  return {
    accessToken: getCookie('access_token'),
    idToken: getCookie('id_token'),
    refreshToken: getCookie('refresh_token'),
  }
}

/**
 * Clears all authentication cookies
 * @param {Object} options - Cookie options
 */
export const clearAuthCookies = (options = {}) => {
  deleteCookie('access_token', options)
  deleteCookie('id_token', options)
  deleteCookie('refresh_token', options)
}
