// utils/validation/helpAndSupportValidation.js

import { isValidNumber } from 'libphonenumber-js'

// Regular expression for validating email addresses
const emailRegex =
  /^[a-zA-Z0-9]+([.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([.][a-zA-Z0-9]+)*[.][a-zA-Z]{2,}$/

/**
 * Validates that the full name is provided and contains at least two words.
 * @param {string} fullName
 * @returns {string|null} An error message or null if valid.
 */
export function validateFullName(fullName) {
  if (!fullName || !fullName.trim()) {
    return 'Full name is required.'
  }
  return null
}

/**
 * Validates that the email address is provided and matches a basic email pattern.
 * @param {string} email
 * @returns {string|null} An error message or null if valid.
 */
export function validateEmail(email) {
  if (!email || !email.trim()) {
    return 'Email is required.'
  }
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address.'
  }
  return null
}

/**
 * Validates that the phone number is provided and consists of 7 to 15 digits.
 * @param {string} phoneNumber
 * @returns {string|null} An error message or null if valid.
 */
export function validatePhoneNumber(phoneNumber) {
  if (!phoneNumber || !phoneNumber.trim()) {
    return 'Phone number is required.'
  }

  if (!isValidNumber(phoneNumber)) {
    return 'Please enter a valid phone number.'
  }
  return null
}

/**
 * Validates that a subject selection has been made.
 * @param {{ value: string }} subject
 * @returns {string|null} An error message or null if valid.
 */
export function validateSubject(subject) {
  if (!subject || !subject.value) {
    return 'Subject is required.'
  }
  return null
}

/**
 * Validates that an brand selection has been made.
 * @param {{ value: any }} brand
 * @param {{ value: string }} brand
 * @returns {string|null} An error message or null if valid.
 */
export function validateBrand(brand) {
  if (!brand || !brand.value) {
    return 'Brand is required.'
  }
  return null
}

/**
 * Validates that an application selection has been made when subject is 'Admission'.
 * @param {{ value: any }} application
 * @param {{ value: string }} subject
 * @returns {string|null} An error message or null if valid.
 */
export function validateApplication(application, subject) {
  if (subject && subject.value === 'Admission') {
    if (!application || !application.value) {
      return 'Application is required when subject is Admission.'
    }
  }
  return null
}

/**
 * Validates that the message is provided and has a minimum length.
 * @param {string} message
 * @returns {string|null} An error message or null if valid.
 */
export function validateMessage(message) {
  if (!message || !message.trim()) {
    return 'Message is required.'
  }
  return null
}

/**
 * Validates the entire form data and returns any errors and an isValid flag.
 * @param {{ fullName: string, email: string, fullNumber: string, subject: object, application: object, message: string }} formData
 * @returns {{ errors: object, isValid: boolean }}
 */
export function validateForm(formData) {
  const errors = {}

  const fullNameError = validateFullName(formData.fullName)
  if (fullNameError) {
    errors.fullName = fullNameError
  }

  const emailError = validateEmail(formData.email)
  if (emailError) {
    errors.email = emailError
  }

  const phoneError = validatePhoneNumber(
    `${formData.countryCode}${formData.phoneNumber}`,
  )
  if (phoneError) {
    errors.phoneNumber = phoneError
  }

  const subjectError = validateSubject(formData.subject)
  if (subjectError) {
    errors.subject = subjectError
  }

  const applicationError = validateApplication(
    formData.application,
    formData.subject,
  )
  if (applicationError) {
    errors.application = applicationError
  }

  const messageError = validateMessage(formData.message)
  if (messageError) {
    errors.message = messageError
  }

  const brandError = validateBrand(formData.brand)
  if (brandError) {
    errors.brand = brandError
  }

  const isValid = Object.keys(errors).length === 0
  return { errors, isValid }
}
