const OAP_URL = {
  UCW_AGENT_PORTAL_DEV: 'https://ucw-oap-dev.apphero.io',
  UCW_OAP_DEV: 'https://apply-dev.ucanwest.ca',
}

export const getOAPUrl = (opporunity) => {
  if (
    !opporunity ||
    !opporunity?.BusinessUnitFilter__c ||
    !opporunity?.ApplicationSource__c
  )
    return ''
  const urlKey = `${
    opporunity.BusinessUnitFilter__c
  }_${opporunity?.ApplicationSource__c?.replace(' ', '_').toUpperCase()}`
  return OAP_URL[`${urlKey}_${process.env.REACT_APP_STAGE?.toUpperCase()}`]
}
