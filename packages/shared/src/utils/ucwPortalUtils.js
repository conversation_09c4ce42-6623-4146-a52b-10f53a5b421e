/**
 * Utility functions for UCW Portal integration
 */

/**
 * Retrieves UCW tokens from localStorage
 * @returns {Object|null} The tokens object or null if not found
 */
export const getUCWTokens = () => {
  try {
    const tokensString = localStorage.getItem('tokens')
    if (!tokensString) {
      return null
    }

    return JSON.parse(tokensString)
  } catch (error) {
    console.error('Error retrieving UCW tokens:', error)
    return null
  }
}

/**
 * Refreshes UCW tokens using the refresh_token
 * @returns {Promise<Object|null>} The new tokens or null if refresh failed
 */
export const refreshUCWTokens = async () => {
  try {
    const tokens = getUCWTokens()
    if (!tokens || !tokens.refresh_token) {
      console.error('No refresh token found')
      return null
    }

    const response = await fetch(
      'https://ucw-student-oap-dev.auth.eu-west-1.amazoncognito.com/oauth2/token',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          client_id: 'mi02thfgb5ql794ejc72c0ae8',
          refresh_token: tokens.refresh_token,
        }),
      },
    )
  } catch (error) {
    console.error('Token refresh failed:', error)
    return null
  }
}

/**
 * Clears UCW tokens from localStorage
 */
export const clearUCWTokens = () => {
  try {
    localStorage.removeItem('ucw_tokens')
    return true
  } catch (error) {
    console.error('Error clearing UCW tokens:', error)
    return false
  }
}

/**
 * Checks if UCW tokens exist and are valid
 * @returns {boolean} True if tokens exist and are valid
 */
export const hasValidUCWTokens = () => {
  const tokens = getUCWTokens()
  if (!tokens || !tokens.access_token) {
    return false
  }

  // Check if token is expired by decoding the JWT
  try {
    const payload = JSON.parse(atob(tokens.access_token.split('.')[1]))
    const expirationTime = payload.exp * 1000 // Convert to milliseconds
    return Date.now() < expirationTime
  } catch (error) {
    console.error('Error validating token:', error)
    return false
  }
}
