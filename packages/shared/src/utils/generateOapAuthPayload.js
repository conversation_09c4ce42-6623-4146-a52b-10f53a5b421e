/**
 * Utility function to generate OAP authentication payload dynamically
 * based on opportunity details and user type/status combinations
 */

/**
 * Generate authentication payload for OAP portal
 * @param {Object} opportunityDetails - The opportunity details object
 * @param {string} userType - 'agent' or 'student'
 * @param {string} status - 'draft' or 'new'
 * @param {string} accessToken - The access token for authentication
 * @param {string} userEmail - The user's email address
 * @returns {Object} The formatted authentication payload
 */
export const generateOapAuthPayload = (
  opportunityDetails,
  userType,
  status,
  accessToken,
  userEmail,
) => {
  // Base payload structure
  const basePayload = {
    type: 'AUTH_REQUEST',
    email: userEmail,
    timestamp: new Date().getTime(),
  }

  // Extract common fields from opportunity details
  const applicationId = opportunityDetails?.ApplicationFormId__c
  const brand =
    opportunityDetails?.brand || opportunityDetails?.institution?.brand || 'UCW'

  // Generate payload based on user type and status combination
  switch (`${userType}_${status}`) {
    case 'agent_draft':
      return {
        ...basePayload,
        applicationId,
        brand,
        userType: 'agent',
        applicationStatus: 'draft',
        agentId: opportunityDetails?.agentId || opportunityDetails?.agent?.id,
        permissions: ['read', 'write', 'submit'],
        context: {
          isDraft: true,
          canEdit: true,
          canSubmit: true,
          source: 'agent_portal',
        },
        access_token: accessToken.access_token,
      }

    case 'agent_new':
      return {
        ...basePayload,
        applicationId,
        brand,
        userType: 'agent',
        applicationStatus: 'new',
        agentId: opportunityDetails?.agentId || opportunityDetails?.agent?.id,
        permissions: ['read', 'write', 'create'],
        context: {
          isDraft: false,
          canEdit: true,
          canCreate: true,
          source: 'agent_portal',
        },
      }

    case 'student_draft':
      return {
        ...basePayload,
        applicationId,
        brand,
        userType: 'student',
        applicationStatus: 'draft',
        permissions: ['read', 'write', 'submit'],
        context: {
          isDraft: true,
          canEdit: true,
          canSubmit: true,
          source: 'student_portal',
        },
        access_token: accessToken.access_token,
      }

    case 'student_new':
      return {
        ...basePayload,
        applicationId,
        brand,
        userType: 'student',
        applicationStatus: 'new',
        studentId:
          opportunityDetails?.studentId || opportunityDetails?.student?.id,
        permissions: ['read', 'create'],
        context: {
          isDraft: false,
          canEdit: false,
          canCreate: true,
          source: 'student_portal',
        },
      }

    default:
      throw new Error(
        `Invalid combination: ${userType}_${status}. Valid combinations are: agent_draft, agent_new, student_draft, student_new`,
      )
  }
}

/**
 * Generate simplified authentication payload (legacy format)
 * @param {Object} opportunityDetails - The opportunity details object
 * @param {string} accessToken - The access token for authentication
 * @param {string} userEmail - The user's email address
 * @returns {Object} The simplified authentication payload
 */
export const generateSimpleOapAuthPayload = (
  opportunityDetails,
  accessToken,
  userEmail,
) => ({
  type: 'AUTH_REQUEST',
  email: userEmail,
  applicationId: opportunityDetails?.applicationId || opportunityDetails?.id,
  brand:
    opportunityDetails?.brand ||
    opportunityDetails?.institution?.brand ||
    'UCW',
  access_token: accessToken,
  timestamp: new Date().getTime(),
})

/**
 * Validate required fields for payload generation
 * @param {Object} opportunityDetails - The opportunity details object
 * @param {string} accessToken - The access token for authentication
 * @param {string} userEmail - The user's email address
 * @returns {boolean} True if all required fields are present
 */
export const validatePayloadRequirements = (
  opportunityDetails,
  accessToken,
  userEmail,
) => {
  if (!opportunityDetails) {
    throw new Error('Opportunity details are required')
  }

  if (!accessToken) {
    throw new Error('Access token is required')
  }

  if (!userEmail) {
    throw new Error('User email is required')
  }

  const applicationId =
    opportunityDetails?.applicationId || opportunityDetails?.id
  if (!applicationId) {
    throw new Error('Application ID is required in opportunity details')
  }

  return true
}
