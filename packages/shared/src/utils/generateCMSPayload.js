import { sort } from './sorting'

const stageMapping = {
  'Documents Stage': '2',
  'Admissions Stage': '3',
  Offer: '4',
  Payment: '5',
  Acceptance: '6',
  Visa: '7',
  'Closed Won': '8',
  'Closed Lost': '9',
  All: '0',
}

const stagePriority = [
  'Closed Won',
  'Visa',
  'Acceptance',
  'Payment',
  'Offer',
  'Admissions Stage',
  'Documents Stage',
  'Closed Lost',
  'All',
]

export function generateCMSInitialPayload(stagesArray) {
  const selectedStage = stagePriority
    .map((priorityStage) =>
      sort(stagesArray, 'updatedAt')?.find(
        (stage) => stage.StageName === priorityStage,
      ),
    )
    .find((stage) => stage !== undefined)

  const initialPayload = {
    applicationStatus: selectedStage
      ? stageMapping[selectedStage?.StageName]
      : '0',
    institution: selectedStage?.BusinessUnitFilter__c,
    country: selectedStage?.location,
    limit: 3,
  }

  return initialPayload
}
