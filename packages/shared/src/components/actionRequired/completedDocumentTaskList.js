import React, { useState } from 'react'
import { CheckBox, Text } from '@libs/components'
import {
  View,
  useWindowDimensions,
  Image,
  TouchableOpacity,
} from 'react-native'
import {
  JpegImage,
  JpgImage,
  MiscImage,
  PdfImage,
  PngImage,
} from '@apphero/assets'
import { useTheme } from '@libs/theme'
import { DateforComments as dateFormat } from '@libs/utils'
import { isWeb } from '@libs/utils/src/screenLayout'
import { DocumentActionList } from './documentActionList'
import { CompletedDocumentTaskListMobile } from './completedDocumentTaskList.mobile'

export const CompletedDocumentTaskList = ({
  item,
  iconNames,
  status,
  activeCheckboxColor,
  businessUnitFilter,
  programmeName,
}) => {
  const { width } = useWindowDimensions()
  const [showFiles, setShowFiles] = useState(false)
  const { colors } = useTheme()

  if (width >= 650) {
    return (
      <View style={{ gap: 12 }}>
        <View
          key={item?.Id}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <CheckBox
            style={{ alignSelf: 'baseline', padding: 0 }}
            disable
            value={status}
            activeCheckboxColor={activeCheckboxColor}
          />
          <View style={{ gap: 5, justifyContent: 'flex-start', flex: 1 }}>
            <Text variant="display5" color="#545B61">
              {dateFormat(item?.CreatedDate)}
            </Text>
            <Text
              variant="display5"
              color="#131E1D"
              style={{ paddingRight: 20 }}
            >
              {item.IsAgentClosed ? (
                <Text variant="display5" style={{ fontWeight: 700 }}>
                  Completed by the Agent -{' '}
                </Text>
              ) : null}
              {item.IsAgentCancelled ? (
                <Text variant="display5" style={{ fontWeight: 700 }}>
                  Cancelled by the Agent -{' '}
                </Text>
              ) : null}
              {item?.IsDirectSalesClosed ? (
                <Text variant="display5" style={{ fontWeight: 700 }}>
                  Closed by Advisor -{' '}
                </Text>
              ) : null}
              {item?.comment}
            </Text>
          </View>
          {!item?.IsDirectSalesClosed &&
          !item.IsAgentClosed &&
          !item.IsAgentCancelled ? (
            <TouchableOpacity
              onPress={() => setShowFiles(!showFiles)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 8,
              }}
            >
              <Text
                variant="display5"
                style={{
                  textDecorationLine: 'underline',
                  fontWeight: !showFiles ? 700 : 400,
                  color: colors.primary,
                }}
              >
                {showFiles ? 'HIDE FILES' : 'SHOW FILES'}
              </Text>
            </TouchableOpacity>
          ) : null}
        </View>

        {showFiles && (
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              gap: 12,
            }}
          >
            {item?.opportunityFileDetails?.map((document, index) => (
              <DocumentItem
                key={document?.documentId}
                index={index}
                item={document}
                documentCount={item?.opportunityFileDetails?.length}
                businessUnitFilter={businessUnitFilter}
                programmeName={programmeName}
                iconNames={iconNames}
              />
            ))}
          </View>
        )}
      </View>
    )
  }
  return (
    <CompletedDocumentTaskListMobile
      item={item}
      iconNames={iconNames}
      status={status}
      activeCheckboxColor={activeCheckboxColor}
      businessUnitFilter={businessUnitFilter}
      programmeName={programmeName}
    />
  )
}

export const DocumentItem = ({
  index,
  item,
  documentCount,
  custonWidth,
  businessUnitFilter,
  programmeName,
  iconNames,
}) => {
  const { width } = useWindowDimensions()
  const isDesktop = isWeb(width)

  const getFileTypeImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  let computedWidth = '49%'
  if (custonWidth || window.innerWidth <= 1200 || documentCount === 1) {
    computedWidth = '100%'
  } else if (window.innerWidth <= 1370) {
    computedWidth = '100%'
  }

  return (
    <View
      style={{
        shadowColor: 'rgba(3, 30, 125, 0.05)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 10,
        elevation: 5,
        borderRadius: 10,
        backgroundColor: '#F3F3F3',
        padding: 16,
        height: 'auto',
        width: computedWidth,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 12,
      }}
      key={index}
    >
      <View
        style={{
          flexDirection: 'row',
          gap: 12,
          justifyContent: 'space-between',
          alignItems: 'center',
          flex: 1,
        }}
      >
        <Image
          source={getFileTypeImage(item?.fileName)}
          style={{ height: 48, width: 40 }}
        />
        <View style={{ flex: 1, justifyContent: 'flex-start', gap: 4 }}>
          <Text
            style={{ fontSize: 13, fontWeight: 700 }}
            numberOfLines={1}
            title={item?.fileName}
          >
            {item?.fileName}
          </Text>
          <Text variant="display4">{item.documentType}</Text>
        </View>
      </View>
      <DocumentActionList
        iconNames={iconNames}
        taskDetails={item}
        businessUnitFilter={businessUnitFilter}
        programmeName={programmeName}
      />
    </View>
  )
}
