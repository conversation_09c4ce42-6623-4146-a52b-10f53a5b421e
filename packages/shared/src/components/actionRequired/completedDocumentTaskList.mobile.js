import React, { useState } from 'react'
import { CheckBox, Text } from '@libs/components'
import { View, Image, TouchableOpacity, StyleSheet } from 'react-native'
import {
  JpegImage,
  JpgImage,
  MiscImage,
  PdfImage,
  PngImage,
} from '@apphero/assets'
import { Icon } from '@app-hero/native-icons'
import { DateforComments as dateFormat } from '@libs/utils'
import { useTheme } from '@libs/theme'
import { DocumentActionList } from './documentActionList'
import BottomSheet from '../Drawer'

export const CompletedDocumentTaskListMobile = ({
  item,
  iconNames,
  status,
  activeCheckboxColor,
  businessUnitFilter,
  programmeName,
}) => {
  const [showFiles, setShowFiles] = useState(false)
  const { colors } = useTheme()

  const getFileTypeImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  return (
    <View style={styles.container}>
      {/* Main Task Item */}
      <View style={styles.taskItem}>
        <CheckBox
          style={{ alignSelf: 'baseline', padding: 0 }}
          disable
          value={status}
          activeCheckboxColor={activeCheckboxColor}
        />

        <View style={styles.taskContent}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Text variant="display5" color="#545B61">
              {dateFormat(item?.CreatedDate)}
            </Text>
          </View>
          <Text variant="display4" color="#131E1D" style={{ paddingRight: 20 }}>
            {item.IsAgentClosed ? (
              <Text variant="display5" style={{ fontWeight: 700 }}>
                Completed by the Agent -{' '}
              </Text>
            ) : null}
            {item.IsAgentCancelled ? (
              <Text variant="display5" style={{ fontWeight: 700 }}>
                Cancelled by the Agent -{' '}
              </Text>
            ) : null}
            {item?.IsDirectSalesClosed ? (
              <Text variant="display5" style={{ fontWeight: 700 }}>
                Closed by Advisor -{' '}
              </Text>
            ) : null}
            {item?.comment}
          </Text>
        </View>
        {!item?.IsDirectSalesClosed &&
        !item.IsAgentClosed &&
        !item.IsAgentCancelled ? (
          <TouchableOpacity
            onPress={() => setShowFiles((prev) => !prev)}
            style={styles.toggleButton}
          >
            <Text
              variant="display6"
              style={{
                fontWeight: showFiles ? '600' : '400',
                color: colors.primary,
              }}
            >
              {showFiles ? 'HIDE FILES' : 'SHOW FILES'}
            </Text>
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Redesigned Accordion Content - File List */}
      {showFiles && (
        <View style={styles.accordionContainer}>
          <View style={styles.accordionHeader}>
            <Text variant="display6" style={styles.accordionTitle}>
              {item?.opportunityFileDetails?.length || 0}{' '}
              {item?.opportunityFileDetails?.length === 1
                ? 'document'
                : 'documents'}
            </Text>
          </View>

          <View style={styles.filesList}>
            {item?.opportunityFileDetails?.map((document, index) => (
              <DocumentItemMobile
                key={document?.documentId}
                item={document}
                iconNames={iconNames}
                businessUnitFilter={businessUnitFilter}
                programmeName={programmeName}
                getFileTypeImage={getFileTypeImage}
                isLast={
                  index === (item?.opportunityFileDetails?.length ?? 0) - 1
                }
              />
            ))}
          </View>
        </View>
      )}
    </View>
  )
}

const DocumentItemMobile = ({
  item,
  iconNames,
  businessUnitFilter,
  programmeName,
  getFileTypeImage,
  isLast = false,
}) => (
  <View style={[styles.documentItem, isLast && styles.documentItemLast]}>
    <View style={styles.documentInfo}>
      <View style={styles.documentIconContainer}>
        <Image
          source={getFileTypeImage(item?.fileName)}
          style={styles.documentIcon}
        />
      </View>
      <View style={styles.documentText}>
        <Text style={styles.fileName} numberOfLines={2} title={item?.fileName}>
          {item?.fileName}
        </Text>
        <Text variant="display6" style={styles.documentType}>
          {item.documentType}
        </Text>
      </View>
    </View>

    <View style={styles.documentActions}>
      <DocumentActionList
        iconNames={iconNames}
        taskDetails={item}
        businessUnitFilter={businessUnitFilter}
        programmeName={programmeName}
        isMobile
      />
    </View>
  </View>
)

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    // backgroundColor: '#FFFFFF',
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.1,
    // shadowRadius: 8,
    // elevation: 4,
    marginVertical: 4,
  },
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 4,
  },
  taskContent: {
    gap: 5,
    justifyContent: 'flex-start',
    flex: 1,
    paddingRight: 12,
  },
  toggleButton: {
    alignSelf: 'baseline',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(79, 143, 255, 0.1)',
  },
  // New accordion styles
  accordionContainer: {
    backgroundColor: '#F8F9FF',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  accordionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    gap: 10,
  },
  fileCountBadge: {
    backgroundColor: '#4F8FFF',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  fileCountText: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 12,
  },
  accordionTitle: {
    color: '#2E3A59',
    fontSize: 12,
  },
  filesList: {
    gap: 8,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 14,
    borderWidth: 1,
    borderColor: '#E8EAED',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  documentItemLast: {
    marginBottom: 0,
  },
  documentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  documentIconContainer: {
    backgroundColor: '#F0F4FF',
    borderRadius: 8,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentIcon: {
    height: 28,
    width: 22,
  },
  documentText: {
    flex: 1,
    gap: 4,
  },
  fileName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A1D23',
    lineHeight: 18,
  },
  documentType: {
    color: '#6B7280',
    fontWeight: '500',
    fontSize: 12,
  },
  documentActions: {
    marginLeft: 8,
  },
  // Legacy styles (kept for compatibility)
  filesContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E5E5',
    marginVertical: 12,
  },
  filesHeader: {
    fontWeight: '600',
    marginBottom: 12,
  },
  bottomSheetDivider: {
    borderWidth: 2,
    borderColor: 'rgba(212, 212, 216, 1)',
    borderStyle: 'solid',
    marginHorizontal: 80,
    borderRadius: 3.5,
    marginBottom: 15,
  },
})
