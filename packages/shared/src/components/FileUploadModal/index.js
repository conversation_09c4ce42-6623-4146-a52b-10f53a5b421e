import { View, StyleSheet, Modal, TouchableOpacity, Image } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, DropDown, FilePicker, Loader, Text } from '@libs/components'

import { Icon } from '@app-hero/native-icons'
import {
  PdfImage,
  PngImage,
  JpegImage,
  JpgImage,
  MiscImage,
} from '@apphero/assets'
import { useTheme } from '@libs/theme'
import { BlurView } from 'expo-blur'
import FileUploadModalMobile from './index.mobile'
import { AlertBox } from '../actionRequired/alertConatiner'
import MultiFileUploadModal from '../MultiFileUploadModal'
import { deleteFileFromS3, generateS3SignedUrl } from '../../api'

const FileUploadModal = ({
  title = '',
  visible = false,
  toggleDropdown = () => {},
  handleSave = async () => {},
  handleClose = () => {},
  dropDownRef,
  dropDownPosition = {},
  dropdownWidth = '',
  documentType = [],
  selectedDropDownValue,
  taskId,
  setModalVisible,
  taskDetails,
  opportunityDetails,
}) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const [file, setFile] = useState({})
  const [error, setError] = useState()
  const [selectedDocumentType, setSelectedDocumentType] = useState({})
  const [isFileSaving, setIsFileSaving] = useState()
  const [removingFile, setRemovingFile] = useState(false)

  // Determine if this is multi-upload mode
  const isMultiUpload = taskDetails?.Follow_Up__c

  const selectedDropDownItem = documentType?.find(
    (item) => item.label === selectedDropDownValue,
  )

  useEffect(() => {
    if (selectedDropDownItem) {
      setSelectedDocumentType(selectedDropDownItem)
    }
  }, [selectedDropDownItem, file])

  const handleSaveDocument = async () => {
    setIsFileSaving(true)
    await handleSave({
      files: [file],
      documentType: selectedDocumentType?.label,
      taskId,
      taskDetails,
    })
    setIsFileSaving(false)

    handleClose()
    setSelectedDocumentType({})
    setFile({})
  }

  if (isMultiUpload) {
    return (
      <MultiFileUploadModal
        title={title}
        visible={visible}
        toggleDropdown={toggleDropdown}
        handleSave={handleSave}
        handleClose={handleClose}
        dropDownRef={dropDownRef}
        dropDownPosition={dropDownPosition}
        dropdownWidth={dropdownWidth}
        documentType={documentType}
        selectedDropDownValue={selectedDropDownValue}
        taskId={taskId}
        setModalVisible={setModalVisible}
        taskDetails={taskDetails}
        opportunityDetails={opportunityDetails}
      />
    )
  }

  const canCTADisable = () => {
    // Check if any files have failed uploads
    const hasFailedFiles = file.status === 'failed'

    // Check if any files are currently uploading
    const hasUploadingFiles =
      file.status === 'preparing' || file.status === 'uploading'

    return (
      Object.keys(selectedDocumentType).length === 0 ||
      Object.keys(file).length === 0 ||
      hasFailedFiles ||
      hasUploadingFiles ||
      !!error
    )
  }
  const getFileTypeImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  const uploadFileToS3 = async (fileJson, rawFile) => {
    try {
      // Set initial state with preparing status
      setFile({
        ...fileJson,
        rawFile, // Store raw file for retry
        status: 'preparing',
        progress: 0,
        uploadMessage: 'Preparing upload...',
        documentType: selectedDocumentType.label,
      })

      // Update status to show we're getting the signed URL
      setFile((prev) => ({
        ...prev,
        status: 'preparing',
        progress: 20,
        uploadMessage: 'Getting upload permission...',
      }))

      setError('')

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: fileJson.contentType,
        documentType: selectedDocumentType.label,
        fileName: fileJson.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFile((prev) => ({
          ...prev,
          status: 'failed',
          progress: 0,
          error: 'Unable to get upload permission',
          uploadMessage: 'Upload failed',
        }))
        global.showToast(
          `Unable to upload outstanding documents at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFile((prev) => ({
        ...prev,
        status: 'uploading',
        progress: 50,
        uploadMessage: 'Uploading to cloud storage...',
      }))

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': fileJson.contentType,
        },
        body: rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFile({
          ...fileJson,
          status: 'completed',
          progress: 100,
          objectKey: getS3SignedUrl.objectKey,
          uploadMessage: 'Upload complete',
          documentType: selectedDocumentType.label,
        })
        setError('')
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (errorDetails) {
      setFile((prev) => ({
        ...prev,
        status: 'failed',
        progress: 0,
        error: errorDetails.message || 'Upload failed',
        uploadMessage: 'Upload failed',
      }))
      global.showToast(
        `Unable to upload outstanding documents at this time. Please try again later.`,
        { type: 'error' },
      )
    } finally {
      // Reset states in finally block to ensure cleanup on failed uploads
      if (file.status === 'failed') {
        setSelectedDocumentType({})
      }
    }
  }

  const retryFileUpload = async () => {
    if (!file.rawFile) return

    try {
      // Update status to show we're retrying
      setFile((prev) => ({
        ...prev,
        status: 'preparing',
        progress: 20,
        error: null,
        uploadMessage: 'Retrying upload...',
      }))

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: file.contentType,
        documentType: selectedDocumentType.label,
        fileName: file.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFile((prev) => ({
          ...prev,
          status: 'failed',
          progress: 0,
          error: 'Unable to get upload permission',
          uploadMessage: 'Upload failed',
        }))
        global.showToast(
          `Unable to retry upload at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFile((prev) => ({
        ...prev,
        status: 'uploading',
        progress: 50,
        uploadMessage: 'Uploading to cloud storage...',
      }))

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.contentType,
        },
        body: file.rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFile({
          ...file,
          status: 'completed',
          progress: 100,
          objectKey: getS3SignedUrl.objectKey,
          uploadMessage: 'Upload complete',
          documentType: selectedDocumentType,
        })
        setError('')
        global.showToast('File uploaded successfully!', { type: 'success' })
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (uploadError) {
      setFile((prev) => ({
        ...prev,
        status: 'failed',
        progress: 0,
        error: uploadError.message || 'Upload failed',
        uploadMessage: 'Upload failed',
      }))
      global.showToast(
        `Unable to retry upload at this time. Please try again later.`,
        { type: 'error' },
      )
    }
  }

  if (window.innerWidth < 650) {
    return (
      <FileUploadModalMobile
        title={title}
        visible={visible}
        toggleDropdown={toggleDropdown}
        handleSave={handleSave}
        handleClose={handleClose}
        dropDownPosition={dropDownPosition}
        dropDownRef={dropDownRef}
        dropdownWidth={dropdownWidth}
        documentType={documentType}
        selectedDropDownValue={selectedDropDownValue}
        taskId={taskId}
        setModalVisible={setModalVisible}
        taskDetails={taskDetails}
        opportunityDetails={opportunityDetails}
      />
    )
  }

  const getFileStatusIcon = (newFile) => {
    switch (newFile.status) {
      case 'preparing':
        return <Loader size={15} />
      case 'uploading':
        return <Loader size={15} />
      case 'completed':
        return (
          <Icon name="Check" height={16} width={16} color={colors.success} />
        )
      case 'failed':
        return (
          <Icon
            name="AlertCircle"
            height={15}
            width={15}
            color={colors.onAlert}
          />
        )
      default:
        return null
    }
  }

  return (
    <Modal
      visible={visible}
      onBackdropPress={() => {
        handleClose()
        setSelectedDocumentType({})
        setFile({})
      }}
      onRequestClose={() => {
        handleClose()
        setSelectedDocumentType({})
        setFile({})
      }}
      transparent
      animationType="none"
      style={{ flex: 1, zIndex: 200 }}
    >
      <BlurView intensity={80} style={styles.overlay}>
        <View style={[styles.container, styles.modalOverlay]}>
          <View style={styles.card}>
            <View style={[styles.header]}>
              <TouchableOpacity
                style={{
                  position: 'absolute',
                  right: window.innerWidth <= 440 ? -0 : -20,
                  top: window.innerWidth <= 440 ? -5 : -15,
                }}
                onPress={() => {
                  handleClose()
                  setSelectedDocumentType({})
                  setFile({})
                }}
              >
                <Icon name="Close" height={23} width={23} />
              </TouchableOpacity>
              <Text
                style={{
                  paddingVertical: 20,
                  fontSize: 24,
                  fontWeight: '700',
                  color: colors.neutral,
                }}
                placeHolder="Text"
              >
                {title}
              </Text>
            </View>
            <View>
              <Text variant="display4" color={colors.neutral}>
                {t('FILE_UPLOAD.DOCUMENT_TYPE')}{' '}
                <Text variant="display4" color={colors.onAlert}>
                  *
                </Text>
              </Text>

              <View ref={dropDownRef}>
                <DropDown
                  items={documentType}
                  toggleDropdown={toggleDropdown}
                  position={dropDownPosition}
                  dropdownWidth={dropdownWidth}
                  onPress={(selectedOption) =>
                    setSelectedDocumentType(selectedOption)
                  }
                  label={t('DROP_DOWN.LABEL_SELECT')}
                  dropdownHeight={190}
                  style={{
                    margin: 0,
                    marginTop: 8,
                    marginBottom: 20,
                  }}
                  placeHolderColor={colors.fieldBorder}
                  value={selectedDropDownItem}
                  disable={selectedDropDownItem}
                />
              </View>
            </View>
            <View>
              <Text
                variant="display4"
                style={{
                  flexWrap: 'wrap',
                  flex: 1,
                }}
                color={colors.neutral}
              >
                {t('FILE_UPLOAD.ATTACH_DOCUMENT')}{' '}
                <Text variant="display4" color={colors.onAlert}>
                  *
                </Text>
              </Text>

              {Object.keys(file).length > 0 ? (
                <View
                  key={file.objectKey}
                  style={[
                    styles.documentCard,
                    removingFile && styles.documentCardRemoving,
                  ]}
                >
                  <View style={styles.cardHeader}>
                    {/* File icon and basic info */}
                    <View style={styles.fileIconContainer}>
                      <Image
                        source={getFileTypeImage(file.name)}
                        style={styles.documentIcon}
                      />
                    </View>

                    <View style={styles.fileBasicInfo}>
                      <Text
                        variant="body2"
                        style={styles.documentName}
                        numberOfLines={1}
                      >
                        {file.name}
                      </Text>
                      <Text variant="display4" style={styles.documentType}>
                        {file.documentType.label}
                      </Text>
                    </View>

                    {/* Status icon and action buttons */}
                    <View style={styles.cardActions}>
                      <View style={styles.statusIconContainer}>
                        {getFileStatusIcon(file)}
                      </View>

                      <View style={styles.actionButtonsContainer}>
                        {/* Show retry button for failed uploads */}
                        {file.status === 'failed' &&
                          file.status !== 'preparing' &&
                          file.status !== 'uploading' && (
                            <TouchableOpacity
                              style={styles.retryButton}
                              onPress={() => retryFileUpload()}
                              disabled={removingFile}
                              accessibilityLabel="Retry upload"
                              accessibilityRole="button"
                            >
                              <Icon
                                name="Refresh"
                                height={16}
                                width={16}
                                color={colors.primary}
                              />
                            </TouchableOpacity>
                          )}

                        {/* Delete button */}
                        <TouchableOpacity
                          style={styles.deleteButton}
                          onPress={async () => {
                            setRemovingFile(true)
                            try {
                              if (file.objectKey) {
                                await deleteFileFromS3({
                                  objectKey: file.objectKey,
                                  BusinessUnitFilter__c:
                                    opportunityDetails.BusinessUnitFilter__c,
                                })
                              }
                              setFile({})
                              setError('')
                            } catch (removeError) {
                              global.showToast(
                                'Unable to remove file. Please try again.',
                                { type: 'error' },
                              )
                            } finally {
                              setRemovingFile(false)
                            }
                          }}
                          disabled={removingFile}
                        >
                          {removingFile ? (
                            <Loader size={15} />
                          ) : (
                            <Icon
                              name="VisaTrash"
                              height={18}
                              width={18}
                              color={colors.primaryIconColor}
                            />
                          )}
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>

                  {/* Status messages and progress */}
                  <View style={styles.cardFooter}>
                    {/* Upload status message */}
                    {(file.status === 'preparing' ||
                      file.status === 'uploading') && (
                      <View style={styles.uploadingContainer}>
                        <Text
                          variant="caption"
                          color={colors.primary}
                          style={styles.uploadStatusText}
                        >
                          {file.uploadMessage}
                        </Text>
                        {/* Progress bar */}
                        <View style={styles.progressContainer}>
                          <View
                            style={[
                              styles.progressBar,
                              { width: `${file.progress}%` },
                            ]}
                          />
                        </View>
                      </View>
                    )}

                    {/* Error message for failed uploads */}
                    {file.status === 'failed' && file.error && (
                      <View style={styles.errorContainer}>
                        <Text
                          variant="caption"
                          color={colors.onAlert}
                          style={styles.errorText}
                        >
                          {file.error}
                        </Text>
                        <Text
                          variant="caption"
                          color={colors.primary}
                          style={styles.retryHintText}
                        >
                          Use the retry button to upload again
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ) : (
                <FilePicker
                  style={{
                    marginBottom: 20,
                  }}
                  handleFileSelect={async ({
                    fileJson,
                    error: fileError,
                    rawFile,
                  }) => {
                    if (fileError) {
                      setError(fileError)
                      return
                    }

                    await uploadFileToS3(fileJson, rawFile)
                  }}
                  isDisabled={!selectedDocumentType.label}
                />
              )}
            </View>
            <View style={{ flex: 1, marginBottom: 20 }}>
              <AlertBox
                iconColor={colors.alertPrimary}
                iconSize={20}
                iconName="AlertCircle"
                style={{
                  flexDirection: 'row',
                  gap: 8,
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <View style={{ flex: 1, flexWrap: 'wrap', width: '80%' }}>
                  <Text variant="display4" color={colors.alertPrimary}>
                    <b>{t('FILE_UPLOAD.NOTE')}</b>
                    {t('FILE_UPLOAD.ALERT')}
                  </Text>
                </View>
              </AlertBox>
            </View>
            {error ? (
              <Text
                variant="display4"
                color={colors.onAlert}
                style={{ marginBottom: 24 }}
              >
                {error}
              </Text>
            ) : null}
            <View style={styles.buttonContainer}>
              {isFileSaving ? (
                <Loader size={20} />
              ) : (
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    gap: 16,
                    flexWrap: 'wrap',
                  }}
                >
                  <Button
                    label={t('BUTTON.SAVE')}
                    buttonColor={colors.primary}
                    onPress={() => handleSaveDocument()}
                    disable={canCTADisable()}
                    labelColors="#FFFF"
                    labelvariant="heading5"
                    buttonStyle={{
                      flex: 1,
                      borderRadius: 4,
                      paddingHorizontal: 83.5,
                      paddingVertical: 10,
                    }}
                    labelStyle={{
                      textTransform: 'uppercase',
                      fontWeight: '700',
                    }}
                  />
                  <Button
                    label={t('BUTTON.CANCEL')}
                    buttonColor="#B6CFF3"
                    onPress={async () => {
                      handleClose()
                      setSelectedDocumentType({})

                      if (file.objectKey) {
                        await deleteFileFromS3({
                          objectKey: file.objectKey,
                          BusinessUnitFilter__c:
                            opportunityDetails.BusinessUnitFilter__c,
                        })
                      }

                      setFile({})
                      setError('')
                    }}
                    appearance="outline"
                    labelColors={colors.primary}
                    buttonStyle={{
                      flex: 1,
                      borderRadius: 4,
                      paddingHorizontal: 83.5,
                      paddingVertical: 10,
                    }}
                    labelStyle={{ textTransform: 'uppercase' }}
                  />
                </View>
              )}
            </View>
          </View>
        </View>
      </BlurView>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000CC',
    width: '100%',
    paddingHorizontal: window.innerWidth <= 440 ? 24 : 40,
    justifyContent: 'center',
  },
  card: {
    alignSelf: 'center',
    backgroundColor: '#ffffff',
    paddingHorizontal: window.innerWidth <= 440 ? 20 : 40,
    paddingVertical: window.innerWidth <= 440 ? 16 : 35,
    justifyContent: 'center',
    maxWidth: 502,
    width: '100%',
    borderRadius: 15,
  },
  header: {
    position: 'relative',
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  overlay: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(11, 92, 215, 0.2)',
  },
  // New document card design matching screenshot
  documentCard: {
    backgroundColor: '#F3F3F3',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
    width: '100%', // Fixed width for horizontal scrolling
    minWidth: 410,
    marginTop: 8,
    marginBottom: 24,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileIconContainer: {
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentIcon: {
    height: 40,
    width: 32,
  },
  fileBasicInfo: {
    flex: 1,
    marginRight: 12,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  documentType: {
    fontSize: 14,
    color: '#666666',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusIconContainer: {
    marginRight: 4,
  },
  cardFooter: {
    minHeight: 'auto',
  },
  uploadingContainer: {
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  uploadStatusText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginTop: 6,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0B5CD7',
    borderRadius: 2,
    transition: 'width 0.3s ease',
  },
  documentActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginLeft: '12px',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  retryButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: 'rgba(11, 92, 215, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(11, 92, 215, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButton: {
    borderRadius: 6,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    marginTop: 4,
    padding: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  retryHintText: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  successText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  documentCardActive: {
    borderWidth: 1,
    borderColor: '#0B5CD7',
    shadowColor: '#0B5CD7',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  documentCardRemoving: {
    opacity: 0.6,
    backgroundColor: '#F0F0F0',
  },
})

export default FileUploadModal
