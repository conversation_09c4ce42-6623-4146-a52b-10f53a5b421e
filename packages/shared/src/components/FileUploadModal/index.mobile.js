import { View, StyleSheet, TouchableOpacity, Image } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, DropDown, FilePicker, Loader, Text } from '@libs/components'

import { Icon } from '@app-hero/native-icons'
import {
  PdfImage,
  PngImage,
  JpegImage,
  JpgImage,
  MiscImage,
} from '@apphero/assets'
import { useTheme } from '@libs/theme'
import BottomSheet from '../Drawer'
import { AlertBox } from '../actionRequired/alertConatiner'
import MultiFileUploadModalMobile from '../MultiFileUploadModal/index.mobile'
import { deleteFileFromS3, generateS3SignedUrl } from '../../api'

const FileUploadModalMobile = ({
  title = '',
  visible = false,
  toggleDropdown = () => {},
  handleSave = async () => {},
  handleClose = () => {},
  dropDownRef,
  dropDownPosition = {},
  dropdownWidth = '',
  documentType = [],
  selectedDropDownValue,
  taskId,
  setModalVisible,
  taskDetails,
  opportunityDetails,
}) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const [file, setFile] = useState({})
  const [error, setError] = useState()
  const [selectedDocumentType, setSelectedDocumentType] = useState({})
  const [isFileSaving, setIsFileSaving] = useState()
  const [removingFile, setRemovingFile] = useState(false)

  // Determine if this is multi-upload mode
  const isMultiUpload = taskDetails?.Follow_Up__c

  const selectedDropDownItem = documentType?.find(
    (item) => item.label === selectedDropDownValue,
  )

  useEffect(() => {
    if (selectedDropDownItem) {
      setSelectedDocumentType(selectedDropDownItem)
    }
  }, [selectedDropDownItem, file])

  const handleSaveDocument = async () => {
    setIsFileSaving(true)
    await handleSave({
      files: [file],
      documentType: selectedDocumentType?.label,
      taskId,
      taskDetails,
    })
    setIsFileSaving(false)

    handleClose()
    setSelectedDocumentType({})
    setFile({})
  }

  if (isMultiUpload) {
    return (
      <MultiFileUploadModalMobile
        title={title}
        visible={visible}
        toggleDropdown={toggleDropdown}
        handleSave={handleSave}
        handleClose={handleClose}
        dropDownRef={dropDownRef}
        dropDownPosition={dropDownPosition}
        dropdownWidth={dropdownWidth}
        documentType={documentType}
        selectedDropDownValue={selectedDropDownValue}
        taskId={taskId}
        setModalVisible={setModalVisible}
        taskDetails={taskDetails}
        opportunityDetails={opportunityDetails}
      />
    )
  }

  const canCTADisable = () => {
    // Check if any files have failed uploads
    const hasFailedFiles = file.status === 'failed'

    // Check if any files are currently uploading
    const hasUploadingFiles =
      file.status === 'preparing' || file.status === 'uploading'

    return (
      Object.keys(selectedDocumentType).length === 0 ||
      Object.keys(file).length === 0 ||
      hasFailedFiles ||
      hasUploadingFiles ||
      !!error
    )
  }
  const getFileTypeImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  const uploadFileToS3 = async (fileJson, rawFile) => {
    try {
      // Set initial state with preparing status
      setFile({
        ...fileJson,
        rawFile, // Store raw file for retry
        status: 'preparing',
        progress: 0,
        uploadMessage: 'Preparing upload...',
        documentType: selectedDocumentType.label,
      })

      // Update status to show we're getting the signed URL
      setFile((prev) => ({
        ...prev,
        status: 'preparing',
        progress: 20,
        uploadMessage: 'Getting upload permission...',
      }))

      setError('')

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: fileJson.contentType,
        documentType: selectedDocumentType.label,
        fileName: fileJson.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFile((prev) => ({
          ...prev,
          status: 'failed',
          progress: 0,
          error: 'Unable to get upload permission',
          uploadMessage: 'Upload failed',
        }))
        global.showToast(
          `Unable to upload outstanding documents at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFile((prev) => ({
        ...prev,
        status: 'uploading',
        progress: 50,
        uploadMessage: 'Uploading to cloud storage...',
      }))

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': fileJson.contentType,
        },
        body: rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFile({
          ...fileJson,
          status: 'completed',
          progress: 100,
          objectKey: getS3SignedUrl.objectKey,
          uploadMessage: 'Upload complete',
          documentType: selectedDocumentType.label,
        })
        setError('')
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (errorDetails) {
      setFile((prev) => ({
        ...prev,
        status: 'failed',
        progress: 0,
        error: errorDetails.message || 'Upload failed',
        uploadMessage: 'Upload failed',
      }))
      global.showToast(
        `Unable to upload outstanding documents at this time. Please try again later.`,
        { type: 'error' },
      )
    } finally {
      // Reset states in finally block to ensure cleanup on failed uploads
      if (file.status === 'failed') {
        setSelectedDocumentType({})
      }
    }
  }

  const retryFileUpload = async () => {
    if (!file.rawFile) return

    try {
      // Update status to show we're retrying
      setFile((prev) => ({
        ...prev,
        status: 'preparing',
        progress: 20,
        error: null,
        uploadMessage: 'Retrying upload...',
      }))

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: file.contentType,
        documentType: selectedDocumentType.label,
        fileName: file.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFile((prev) => ({
          ...prev,
          status: 'failed',
          progress: 0,
          error: 'Unable to get upload permission',
          uploadMessage: 'Upload failed',
        }))
        global.showToast(
          `Unable to retry upload at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFile((prev) => ({
        ...prev,
        status: 'uploading',
        progress: 50,
        uploadMessage: 'Uploading to cloud storage...',
      }))

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.contentType,
        },
        body: file.rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFile({
          ...file,
          status: 'completed',
          progress: 100,
          objectKey: getS3SignedUrl.objectKey,
          uploadMessage: 'Upload complete',
          documentType: selectedDocumentType,
        })
        setError('')
        global.showToast('File uploaded successfully!', { type: 'success' })
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (uploadError) {
      setFile((prev) => ({
        ...prev,
        status: 'failed',
        progress: 0,
        error: uploadError.message || 'Upload failed',
        uploadMessage: 'Upload failed',
      }))
      global.showToast(
        `Unable to retry upload at this time. Please try again later.`,
        { type: 'error' },
      )
    }
  }

  const getFileStatusIcon = (newFile) => {
    switch (newFile.status) {
      case 'preparing':
        return <Loader size={15} />
      case 'uploading':
        return <Loader size={15} />
      case 'completed':
        return (
          <Icon name="Check" height={16} width={16} color={colors.success} />
        )
      case 'failed':
        return (
          <Icon
            name="AlertCircle"
            height={15}
            width={15}
            color={colors.onAlert}
          />
        )
      default:
        return null
    }
  }

  return (
    <BottomSheet
      drawerHeight={490}
      isVisible={visible}
      setIsVisible={setModalVisible}
      style={{
        backgroundColor: '#fff',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
      }}
    >
      <View style={styles.topdivider} />
      <View style={[styles.container, styles.modalOverlay]}>
        <View style={styles.card}>
          <View style={[styles.header]}>
            <Text
              style={{
                paddingVertical: 20,
                fontSize: 24,
                fontWeight: '700',
                color: colors.neutral,
              }}
              placeHolder="Text"
            >
              {title}
            </Text>
          </View>
          <View>
            <Text variant="display4" color={colors.neutral}>
              {t('FILE_UPLOAD.DOCUMENT_TYPE')}{' '}
              <Text variant="display4" color={colors.onAlert}>
                *
              </Text>
            </Text>

            <View ref={dropDownRef}>
              <DropDown
                items={documentType}
                toggleDropdown={toggleDropdown}
                position={dropDownPosition}
                dropdownWidth={dropdownWidth}
                onPress={(selectedOption) =>
                  setSelectedDocumentType(selectedOption)
                }
                label={t('DROP_DOWN.LABEL_SELECT')}
                dropdownHeight={190}
                style={{
                  margin: 0,
                  marginTop: 8,
                  marginBottom: 20,
                }}
                placeHolderColor={colors.fieldBorder}
                value={
                  file.documentType
                    ? { label: file.documentType }
                    : selectedDropDownItem
                }
                disable={selectedDropDownItem}
              />
            </View>
          </View>
          <View>
            <Text
              variant="display4"
              style={{
                flexWrap: 'wrap',
                flex: 1,
              }}
              color={colors.neutral}
            >
              {t('FILE_UPLOAD.ATTACH_DOCUMENT')}{' '}
              <Text variant="display4" color={colors.onAlert}>
                *
              </Text>
            </Text>
            {Object.keys(file).length > 0 ? (
              <View
                key={file.objectKey}
                style={[
                  styles.documentCard,
                  removingFile && styles.documentCardRemoving,
                ]}
              >
                <View style={styles.cardHeader}>
                  {/* File icon and basic info */}
                  <View style={styles.fileIconContainer}>
                    <Image
                      source={getFileTypeImage(file.name)}
                      style={styles.documentIcon}
                    />
                  </View>

                  <View style={styles.fileBasicInfo}>
                    <Text
                      variant="body2"
                      style={styles.documentName}
                      numberOfLines={1}
                    >
                      {file.name}
                    </Text>
                    <Text variant="display4" style={styles.documentType}>
                      {file.documentType?.label || selectedDocumentType?.label}
                    </Text>
                  </View>

                  {/* Status icon and action buttons */}
                  <View style={styles.cardActions}>
                    <View style={styles.statusIconContainer}>
                      {getFileStatusIcon(file)}
                    </View>

                    <View style={styles.actionButtonsContainer}>
                      {/* Show retry button for failed uploads */}
                      {file.status === 'failed' &&
                        file.status !== 'preparing' &&
                        file.status !== 'uploading' && (
                          <TouchableOpacity
                            style={styles.retryButton}
                            onPress={() => retryFileUpload()}
                            disabled={removingFile}
                            accessibilityLabel="Retry upload"
                            accessibilityRole="button"
                          >
                            <Icon
                              name="Refresh"
                              height={16}
                              width={16}
                              color={colors.primary}
                            />
                          </TouchableOpacity>
                        )}

                      {/* Delete button */}
                      <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={async () => {
                          setRemovingFile(true)
                          try {
                            if (file.objectKey) {
                              await deleteFileFromS3({
                                objectKey: file.objectKey,
                                BusinessUnitFilter__c:
                                  opportunityDetails.BusinessUnitFilter__c,
                              })
                            }
                            setFile({})
                            setError('')
                          } catch (removeError) {
                            global.showToast(
                              'Unable to remove file. Please try again.',
                              { type: 'error' },
                            )
                          } finally {
                            setRemovingFile(false)
                          }
                        }}
                        disabled={removingFile}
                      >
                        {removingFile ? (
                          <Loader size={15} />
                        ) : (
                          <Icon
                            name="VisaTrash"
                            height={18}
                            width={18}
                            color={colors.primaryIconColor}
                          />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>

                {/* Status messages and progress */}
                <View style={styles.cardFooter}>
                  {/* Upload status message */}
                  {(file.status === 'preparing' ||
                    file.status === 'uploading') && (
                    <View style={styles.uploadingContainer}>
                      <Text
                        variant="caption"
                        color={colors.primary}
                        style={styles.uploadStatusText}
                      >
                        {file.uploadMessage}
                      </Text>
                      {/* Progress bar */}
                      <View style={styles.progressContainer}>
                        <View
                          style={[
                            styles.progressBar,
                            { width: `${file.progress}%` },
                          ]}
                        />
                      </View>
                    </View>
                  )}

                  {/* Error message for failed uploads */}
                  {file.status === 'failed' && file.error && (
                    <View style={styles.errorContainer}>
                      <Text
                        variant="caption"
                        color={colors.onAlert}
                        style={styles.errorText}
                      >
                        {file.error}
                      </Text>
                      <Text
                        variant="caption"
                        color={colors.primary}
                        style={styles.retryHintText}
                      >
                        Use the retry button to upload again
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            ) : (
              <FilePicker
                style={{
                  marginBottom: 20,
                }}
                handleFileSelect={async ({
                  fileJson,
                  error: fileError,
                  rawFile,
                }) => {
                  if (fileError) {
                    setError(fileError)
                    return
                  }

                  await uploadFileToS3(fileJson, rawFile)
                }}
                isDisabled={!selectedDocumentType.label}
              />
            )}
            <View style={{ flex: 1, marginBottom: 32 }}>
              <AlertBox
                iconColor={colors.alertPrimary}
                iconSize={20}
                iconName="AlertCircle"
                style={{
                  flexDirection: 'row',
                  gap: 8,
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <View style={{ flex: 1, flexWrap: 'wrap', width: '80%' }}>
                  <Text variant="display4" color={colors.alertPrimary}>
                    <b>{t('FILE_UPLOAD.NOTE')}</b>
                    {t('FILE_UPLOAD.ALERT')}
                  </Text>
                </View>
              </AlertBox>
            </View>
          </View>
          {error ? (
            <Text
              variant="display4"
              color={colors.onAlert}
              style={{ marginBottom: 20 }}
            >
              {error}
            </Text>
          ) : null}
          <View style={styles.buttonContainer}>
            {isFileSaving ? (
              <Loader size={20} />
            ) : (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'column',
                  gap: 16,
                }}
              >
                <Button
                  label={t('BUTTON.SAVE')}
                  buttonColor={colors.primary}
                  onPress={() => handleSaveDocument()}
                  disable={canCTADisable()}
                  labelColors="#FFFF"
                  labelvariant="heading5"
                  buttonStyle={{
                    flex: 1,
                    borderRadius: 4,
                    paddingHorizontal: 83.5,
                    paddingVertical: 10,
                  }}
                  labelStyle={{
                    textTransform: 'uppercase',
                    fontWeight: '700',
                  }}
                />
                <Button
                  label={t('BUTTON.CANCEL')}
                  buttonColor="#B6CFF3"
                  onPress={async () => {
                    handleClose()
                    setSelectedDocumentType({})
                    if (file.objectKey) {
                      await deleteFileFromS3({
                        objectKey: file.objectKey,
                        BusinessUnitFilter__c:
                          opportunityDetails.BusinessUnitFilter__c,
                      })
                    }
                    setFile({})
                    setError('')
                  }}
                  appearance="outline"
                  labelColors={colors.primary}
                  buttonStyle={{
                    flex: 1,
                    borderRadius: 4,
                    paddingHorizontal: 83.5,
                    paddingVertical: 10,
                  }}
                  labelStyle={{ textTransform: 'uppercase' }}
                />
              </View>
            )}
          </View>
        </View>
      </View>
    </BottomSheet>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    width: '100%',
    paddingHorizontal: 8,
  },
  card: {
    width: '100%',
    borderRadius: 15,
  },
  header: {
    position: 'relative',
  },
  buttonContainer: {
    flexDirection: 'column',
  },
  overlay: {
    flex: 1,
  },
  topdivider: {
    borderWidth: 2,
    borderColor: 'rgba(212, 212, 216, 1)',
    borderStyle: 'solid',
    marginHorizontal: 80,
    borderRadius: 3.5,
    marginBottom: 15,
  },
  // New document card design matching screenshot
  documentCard: {
    backgroundColor: '#F3F3F3',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
    width: '100%',
    marginTop: 8,
    marginBottom: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileIconContainer: {
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentIcon: {
    height: 32,
    width: 24,
  },
  fileBasicInfo: {
    flex: 1,
    marginRight: 8,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  documentType: {
    fontSize: 12,
    color: '#666666',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusIconContainer: {
    marginRight: 4,
  },
  cardFooter: {
    minHeight: 'auto',
  },
  uploadingContainer: {
    marginTop: 4,
  },
  errorContainer: {
    marginTop: 4,
    padding: 6,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  retryButton: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(11, 92, 215, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(11, 92, 215, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 2,
  },
  retryHintText: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  uploadStatusText: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 4,
  },
  progressContainer: {
    width: '100%',
    height: 3,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginTop: 6,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0B5CD7',
    borderRadius: 2,
  },
  documentActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  deleteButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentCardActive: {
    borderWidth: 1,
    borderColor: '#0B5CD7',
    shadowColor: '#0B5CD7',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  documentCardRemoving: {
    opacity: 0.6,
    backgroundColor: '#F0F0F0',
  },
})

export default FileUploadModalMobile
