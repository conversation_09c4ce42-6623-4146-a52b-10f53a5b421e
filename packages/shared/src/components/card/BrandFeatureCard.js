import { View, StyleSheet, Image } from 'react-native'
import React from 'react'
import { Text } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { useTheme } from '@libs/theme'

const BrandFeatureCard = (props) => {
  const { items } = props
  const totalItem = items?.length - 1
  return (
    <View
      style={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <View style={styles.subContainer}>
        {items.map((item, index) => (
          <ContentCard {...item} index={index} totalItem={totalItem} />
        ))}
      </View>
    </View>
  )
}

const ContentCard = ({
  title = '',
  content = '',
  iconName = '',
  index,
  totalItem,
}) => {
  const { colors } = useTheme()
  return (
    <View style={styles.contentContainer} key={index}>
      <View
        style={[
          styles.subContentContainer,
          { position: 'relative', height: 148 },
        ]}
      >
        <View style={styles.titleStyle(index)}>
          <Text
            variant="heading6"
            color={colors.white}
            style={{ width: '75%' }}
          >
            {title}
          </Text>
        </View>
        <View style={styles.contentStyle(index)}>
          <View
            style={{
              padding: 16,
              width: '80%',
            }}
          >
            <Text
              variant="display4"
              color={colors.white}
              style={{ padding: 8 }}
            >
              {content}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.icon(index)}>
        <Image
          source={iconName}
          style={{
            height: 100,
            width: 120,
          }}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 35,
    backgroundColor: 'rgba(256,256,256,0.3)',
    borderRadius: 20,
    marginBottom: 40,
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
    marginBottom: 30,
    backgroundColor: 'rgba(1, 13, 80, 0.5)',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: (index) => ({
    borderRadius: 15,
    marginRight: 20,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 50,
    top: index % 2 === 0 ? -6 : 38,
    right: index % 2 === 0 ? -50 : undefined,
    left: index % 2 === 0 ? undefined : 0,
  }),
  contentStyle: (index) => ({
    width: '100%',
    flex: 1,
    alignItems: index % 2 === 0 ? 'flex-start' : 'flex-end',
  }),
  titleStyle: (index) => ({
    backgroundColor: '#010D50',
    alignItems: index % 2 === 0 ? 'flex-start' : 'flex-end',
    borderRadius: 16,
    height: 48,
    paddingTop: 12,
    padding: 16,
  }),
  subContainer: {
    flex: 1,
    borderRadius: 20,
    width: '85%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  subContentContainer: {
    flexDirection: 'column',
    flex: 1,
  },
})

export default BrandFeatureCard
