import React, { useState } from 'react'
import {
  TouchableOpacity,
  View,
  Text,
  Image,
  useWindowDimensions,
} from 'react-native'
import {
  JpegImage,
  JpgImage,
  MiscImage,
  PdfImage,
  PngImage,
} from '@apphero/assets'
import { Loader } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { isWeb } from '@libs/utils/src/screenLayout'

export const DocumentList = ({
  index,
  item,
  onPress,
  style,
  customWidth,
  isUploaded,
  deleteDocument,
}) => {
  const [isFetching, setIsFetching] = useState(false)
  const { width } = useWindowDimensions()
  const isDesktop = isWeb(width)

  const getFileTypeImage = (fileName) => {
    const extension = fileName?.split('.')?.pop()?.toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''

    const date = new Date(dateString)

    // Get day with ordinal suffix (1st, 2nd, 3rd, etc.)
    const day = date.getDate()
    const suffix = getDaySuffix(day)

    // Format date as "24th Mar 2024"
    const month = date.toLocaleString('en-US', { month: 'short' })
    const year = date.getFullYear()

    return `${day}${suffix} ${month} ${year}`
  }

  const getDaySuffix = (day) => {
    if (day > 3 && day < 21) return 'th'
    switch (day % 10) {
      case 1:
        return 'st'
      case 2:
        return 'nd'
      case 3:
        return 'rd'
      default:
        return 'th'
    }
  }

  return (
    <View
      style={{
        shadowColor: 'rgba(3, 30, 125, 0.05)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 10,
        elevation: 5,
        borderRadius: 10,
        backgroundColor: '#F3F3F3',
        paddingHorizontal: !isDesktop ? 0 : 32,
        paddingVertical: !isDesktop ? 0 : 25,
        width: customWidth || '100%',
      }}
      key={index}
    >
      <View
        style={{
          flexDirection: 'row',
          gap: 24,
          paddingVertical: !isDesktop ? 18 : 0,
          paddingHorizontal: !isDesktop ? 16 : 0,
          justifyContent: 'space-between',
        }}
      >
        <Image
          source={getFileTypeImage(item?.Name || item.name)}
          style={{ height: 48, width: 40 }}
        />
        <View style={{ flex: 1, justifyContent: 'flex-start', gap: 4 }}>
          <Text style={style} numberOfLines={2}>
            {item?.Name || item?.name}
          </Text>
          <Text style={{ fontSize: 14, color: '#162447' }}>
            {formatDate(item?.createdAt)} |{' '}
            {item?.Name?.split('.').pop().toUpperCase() ||
              item?.type?.toUpperCase()}
          </Text>
        </View>
        {isUploaded &&
          (isFetching ? (
            <View>
              <Loader
                size={20}
                style={{
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}
              />
            </View>
          ) : (
            <TouchableOpacity
              onPress={async () => {
                setIsFetching(true)
                await onPress()
                setIsFetching(false)
              }}
              style={{
                flexDirection: 'row',
                justifyContent: 'flex-end',
                alignItems: 'center',
              }}
            >
              <Icon name="Download" />
            </TouchableOpacity>
          ))}
        {!isUploaded && (
          <TouchableOpacity
            onPress={async () => deleteDocument(item)}
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-end',
              alignItems: 'center',
            }}
          >
            <Icon name="VisaTrash" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}
