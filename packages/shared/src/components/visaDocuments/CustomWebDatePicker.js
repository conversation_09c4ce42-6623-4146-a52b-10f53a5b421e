import React, { useRef, useState, useEffect } from 'react'
import { View, StyleSheet, TouchableOpacity } from 'react-native'
import { Text } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import { useTheme } from '@libs/theme'

// Custom styles for the datepicker to fix z-index issues
const customStyles = `
  /* Z-index hierarchy for proper stacking context */
  .custom-datepicker-wrapper {
    position: relative;
    z-index: 1;
    width: 100%;
    flex: 1;
    min-width: 0;
  }
  
  .custom-datepicker-wrapper.focused {
    z-index: 9999;
  }
  
  /* Full override of react-datepicker styles for proper positioning */
  .react-datepicker-popper {
    z-index: 9999 !important; 
    position: fixed !important;
  }
  
  .react-datepicker {
    font-family: Arial, sans-serif;
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    background-color: white;
    padding: 12px;
  }
  
  .react-datepicker__triangle {
    display: none;
  }
  
  .react-datepicker__month-container {
    padding-bottom: 10px;
  }
  
  .react-datepicker__header {
    background-color: white;
    border-bottom: 1px solid #f0f0f0;
    padding-top: 10px;
  }
  
  .react-datepicker__current-month {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
  }
  
  .react-datepicker__day-name {
    color: #666;
    font-weight: 500;
    width: 36px;
    height: 36px;
    margin: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  .react-datepicker__day {
    width: 36px;
    height: 36px;
    margin: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #333;
    font-weight: 400;
    transition: all 0.2s ease;
  }
  
  .react-datepicker__day:hover {
    background-color: #f0f7ff;
    color: #0066ff;
  }
  
  .react-datepicker__day--selected {
    background-color: #0066ff !important;
    color: white !important;
    font-weight: 600;
  }
  
  .react-datepicker__day--keyboard-selected {
    background-color: rgba(0, 102, 255, 0.1);
    color: #0066ff;
  }
  
  .react-datepicker__day--disabled {
    color: #ccc;
  }
  
  .react-datepicker__navigation {
    top: 12px;
  }
  
  .react-datepicker__navigation--previous {
    left: 12px;
  }
  
  .react-datepicker__navigation--next {
    right: 12px;
  }
  
  /* Create a portal container for the date picker */
  #date-picker-portal {
    position: relative;
    z-index: 9999;
  }
  
  /* Full width wrapper */
  .date-picker-full-width {
    display: flex;
    width: 100%;
    min-width: 0;
  }
  
  .react-datepicker-wrapper {
    width: 100%;
    display: block;
    flex: 1;
    min-width: 0;
  }
`

// Custom input component for the date picker
// eslint-disable-next-line react/display-name
const CustomDateInput = React.forwardRef(
  // eslint-disable-next-line no-unused-vars
  ({ value, onClick, hasError, placeholder }, ref) => (
    <TouchableOpacity
      style={[
        styles.dateInput,
        hasError && styles.inputError,
        { flexDirection: 'row', alignItems: 'center', gap: 14 },
      ]}
      onPress={onClick}
    >
      <Icon name="VisaCalendar" width={22} height={22} color="#000000" />
      <Text
        style={[
          styles.dateText,
          value ? styles.dateTextSelected : styles.dateTextPlaceholder,
        ]}
      >
        {value || placeholder}
      </Text>
    </TouchableOpacity>
  ),
)

const CustomWebDatePicker = ({
  value,
  onChange,
  error,
  maxDate,
  placeholder = 'Select date',
  label,
  required = false,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const wrapperRef = useRef(null)

  const { colors } = useTheme()
  // Inject custom styles
  useEffect(() => {
    const styleElement = document.createElement('style')
    styleElement.textContent = customStyles
    document.head.appendChild(styleElement)

    // Create a portal container for the date picker
    if (!document.getElementById('date-picker-portal')) {
      const portalContainer = document.createElement('div')
      portalContainer.id = 'date-picker-portal'
      document.body.appendChild(portalContainer)
    }

    return () => {
      document.head.removeChild(styleElement)
    }
  }, [])

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label(colors)}>
          {label}
          {required && <Text style={styles.errorIndicator}> *</Text>}
        </Text>
      )}

      <div
        className={`custom-datepicker-wrapper ${isOpen ? 'focused' : ''}`}
        ref={wrapperRef}
        style={{
          position: 'relative',
          zIndex: isOpen ? 9999 : 1,
          width: '100%',
          flex: 1,
          minWidth: 0,
        }}
      >
        <DatePicker
          selected={value ? new Date(value) : null}
          onChange={(date) => {
            if (date) {
              // Format date as YYYY-MM-DD
              const year = date.getFullYear()
              const month = String(date.getMonth() + 1).padStart(2, '0')
              const day = String(date.getDate()).padStart(2, '0')
              const formattedDate = `${year}-${month}-${day}`

              // Pass the formatted date - parent component will handle array conversion if needed
              onChange(formattedDate)
            } else {
              onChange(null)
            }
            setIsOpen(false)
          }}
          onCalendarOpen={() => setIsOpen(true)}
          onCalendarClose={() => setIsOpen(false)}
          maxDate={maxDate}
          dateFormat="MMM dd yyyy"
          placeholderText={placeholder}
          popperPlacement="bottom-center"
          portalId="date-picker-portal"
          popperProps={{
            strategy: 'fixed',
          }}
          customInput={
            <CustomDateInput hasError={!!error} placeholder={placeholder} />
          }
          shouldCloseOnSelect
          showPopperArrow={false}
          wrapperClassName="date-picker-full-width"
        />
      </div>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    flex: 1,
    minWidth: 0,
  },
  label: (colors) => ({
    marginBottom: 12,
    fontWeight: '400',
    color: colors.textSecondary,
    fontSize: 14,
    width: '100%',
  }),
  dateInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 11,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxSizing: 'border-box',
    minWidth: 0,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '400',
    flex: 1,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  dateTextSelected: (colors) => ({
    color: colors.textPrimary,
    fontWeight: '500',
  }),
  dateTextPlaceholder: {
    color: '#A1A1AA',
    fontWeight: '400',
  },
  inputError: {
    borderColor: '#D72C2C',
    borderWidth: 1,
    backgroundColor: 'rgba(255, 59, 48, 0.05)',
  },
  errorText: {
    color: '#D72C2C',
    fontSize: 12,
    marginTop: 6,
    fontWeight: '500',
  },
  errorIndicator: {
    color: '#D72C2C',
    fontWeight: '800',
  },
})

export default CustomWebDatePicker
