import { Icon } from '@app-hero/native-icons'
import { Text } from '@libs/components'
import { useTheme } from '@libs/theme'
import { getBase64, isFileValid } from '@libs/utils/src/fileHandler/index.web'
import { isWeb } from '@libs/utils/src/screenLayout'
import React, { useRef } from 'react'
import { TouchableOpacity, View, useWindowDimensions } from 'react-native'

export const CustomFilePicker = ({
  handleFileSelect = () => {},
  style = {},
  isProfile,
}) => {
  const { colors } = useTheme()

  const documentRef = useRef()
  const fileTypes = isProfile
    ? ['JPG', 'JPEG', 'PNG']
    : ['JPG', 'JPEG', 'PDF', 'PNG']
  const { width } = useWindowDimensions()

  const isDesktop = isWeb(width)

  const handleDragOver = (event) => {
    event.preventDefault()
  }

  const handleDrop = async (event) => {
    event.preventDefault()
    await handleFilePicker({
      event,
      fileTypes,
      handleFileSelect,
    })
  }

  return (
    <View style={[{ marginBottom: 30 }, style]}>
      <form
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onSubmit={(e) => {
          handleFilePicker({
            event: e,
            fileTypes,
            handleFileSelect,
          })
        }}
      >
        <View
          style={{
            minHeight: 104,
            borderRadius: 4,
            borderWidth: 2,
            paddingHorizontal: isDesktop ? 71 : 0,
            paddingVertical: 20,
            marginTop: 10,
            borderColor: colors.fieldBorder,
            borderStyle: 'dashed',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
            flexWrap: !isDesktop ? 'wrap' : 'nowrap',
            gap: 4,
          }}
        >
          <View>
            <Icon name="AddFile" height={41} width={41} />
          </View>
          <View style={{ marginLeft: 10 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
              }}
            >
              <TouchableOpacity
                style={{
                  backgroundColor: colors.backgroundOnNeutral,
                  borderRadius: 3,
                  marginRight: 3,
                  paddingHorizontal: 3,
                }}
                onPress={() => documentRef.current.click()}
              >
                <Text
                  variant="heading6"
                  color={colors.white}
                  style={{
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 3,
                    borderColor: colors.primary,
                    borderWidth: 1,
                    color: colors.primary,
                    backgroundColor: '#ECF2FC',
                  }}
                >
                  Upload File
                </Text>
              </TouchableOpacity>
              <Text variant="display4">or drag and drop</Text>
            </View>
            <Text variant="display4" color={colors.primaryIconColor}>
              {fileTypes.map((item) => item).join(', ')} upto to 5MB
            </Text>
          </View>
        </View>

        <input
          type="file"
          ref={documentRef}
          style={{ display: 'none' }}
          onChange={(e) => {
            handleFilePicker({
              event: e,
              fileTypes,
              handleFileSelect,
            })
            e.target.value = null
          }}
          accept={
            isProfile ? '.jpg,.jpeg,.png' : "'.jpg', '.jpeg', '.pdf', '.png'"
          }
        />
      </form>
    </View>
  )
}

const handleFilePicker = async ({ event, fileTypes, handleFileSelect }) => {
  const uploadedFile =
    event.dataTransfer?.files?.[0] || event.target?.files?.[0]

  if (uploadedFile) {
    const isValidFile = uploadedFile
      ? isFileValid({ uploadedFile, fileTypes })
      : false
    const base64Docs = await getBase64(uploadedFile)
    const fileJson = {
      name: uploadedFile?.name,
      size: uploadedFile?.size,
      type: uploadedFile?.type.split('/')[1],
      contentType: uploadedFile?.type,
    }
    const fileURI = URL.createObjectURL(uploadedFile)
    if (isValidFile?.isValid) {
      handleFileSelect({
        file: base64Docs?.split(',')[1],
        fileName: uploadedFile.name,
        selectedFile: fileURI,
        fileJson,
      })
    } else {
      handleFileSelect({
        fileJson,
        error: isValidFile.message,
      })
    }
  }
}
