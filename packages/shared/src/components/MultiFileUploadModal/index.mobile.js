import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
} from 'react-native'
import React, { useEffect, useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, DropDown, Loader, Text } from '@libs/components'

import { Icon } from '@app-hero/native-icons'
import {
  PdfImage,
  PngImage,
  JpegImage,
  JpgImage,
  MiscImage,
} from '@apphero/assets'
import { useTheme } from '@libs/theme'
import BottomSheet from '../Drawer'
import { deleteFileFromS3, generateS3SignedUrl } from '../../api'

const MultiFileUploadModalMobile = ({
  title = '',
  visible = false,
  toggleDropdown = () => {},
  handleSave = async () => {},
  handleClose = () => {},
  dropDownRef,
  dropDownPosition = {},
  dropdownWidth = '',
  documentType = [],
  selectedDropDownValue,
  taskId,
  setModalVisible,
  taskDetails,
  maxFiles = 5,
  maxFileSize = 5242880, // 5MB in bytes
  allowedFileTypes = ['JPG', 'JPEG', 'PDF', 'PNG'],
  opportunityDetails,
}) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const [files, setFiles] = useState([])
  const [error, setError] = useState()
  const [selectedDocumentType, setSelectedDocumentType] = useState({})
  const [isFileSaving, setIsFileSaving] = useState()
  const [isAgreed, setIsAgreed] = useState(false)
  const [showAnotherFile, setShowAnotherFile] = useState(false)
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [currentDocumentType, setCurrentDocumentType] = useState({})
  const [removingFileId, setRemovingFileId] = useState(null)
  const [shouldScrollToEnd, setShouldScrollToEnd] = useState(false)
  const flatListRef = useRef(null)

  const selectedDropDownItem = documentType?.find(
    (item) => item.label === selectedDropDownValue,
  )

  useEffect(() => {
    if (selectedDropDownItem) {
      setSelectedDocumentType(selectedDropDownItem)
    }
  }, [selectedDropDownItem])

  // Effect to handle auto-scrolling to newly added files
  useEffect(() => {
    if (shouldScrollToEnd && files.length > 0 && flatListRef.current) {
      console.log('Auto-scrolling to end of file list', files.length)
      const lastIndex = files.length - 1
      setCurrentCardIndex(lastIndex)

      // Scroll to the newly added file with a delay to ensure rendering
      setTimeout(() => {
        if (flatListRef.current && files.length > 0) {
          try {
            flatListRef.current.scrollToIndex({
              index: lastIndex,
              animated: true,
              viewPosition: 0.5, // Center the item
            })
          } catch (scrollError) {
            // Fallback to scrollToEnd if scrollToIndex fails
            flatListRef.current.scrollToEnd({ animated: true })
          }
        }
      }, 200)

      setShouldScrollToEnd(false)
    }
  }, [files.length, shouldScrollToEnd])

  const validateFile = (file) => {
    const errors = []

    // Check file size
    if (file.size > maxFileSize) {
      errors.push(
        `File size must be less than ${Math.round(maxFileSize / 5242880)}MB`,
      )
    }

    // Check file type
    const fileExtension = file.name.split('.').pop().toUpperCase()
    if (!allowedFileTypes.includes(fileExtension)) {
      errors.push(`File type must be one of: ${allowedFileTypes.join(', ')}`)
    }

    // Check file name length
    const baseName = file.name.split('.').slice(0, -1).join('.')
    if (baseName.length > 70) {
      errors.push('File name must be 70 characters or less')
    }

    return errors
  }

  const handleFileSelect = async ({
    fileJson,
    error: fileError,
    file: selectedFile,
    documentType: fileDocumentType,
  }) => {
    if (fileError) {
      setError(fileError)
      return
    }

    // Check if we've reached the maximum number of files
    if (files.length >= maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`)
      return
    }

    // Validate the file
    const validationErrors = validateFile(fileJson)
    if (validationErrors.length > 0) {
      setError(validationErrors[0])
      return
    }

    const newFile = {
      ...fileJson,
      file: selectedFile,
      rawFile: fileJson.rawFile, // Store raw file for retry
      id: Date.now() + Math.random(), // Unique identifier
      status: 'preparing', // preparing, uploading, completed, failed
      progress: 0,
      error: null,
      documentType: fileDocumentType || currentDocumentType,
      uploadMessage: 'Preparing upload...',
    }

    // Add file to list immediately to show preparation status
    setFiles((prev) => {
      const updatedFiles = [...prev, newFile]

      // Trigger auto-scroll to the newly added file with a small delay
      setTimeout(() => {
        setShouldScrollToEnd(true)
        setCurrentCardIndex(updatedFiles.length - 1) // Set current index to the new file
      }, 50)

      return updatedFiles
    })

    try {
      // Update status to show we're getting the signed URL
      setFiles((prev) =>
        prev.map((file) =>
          file.id === newFile.id
            ? {
                ...file,
                status: 'preparing',
                progress: 20,
                uploadMessage: 'Getting upload permission...',
              }
            : file,
        ),
      )

      setShowAnotherFile(false)

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: fileJson.contentType,
        documentType: fileDocumentType.label,
        fileName: fileJson.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFiles((prev) =>
          prev.map((file) =>
            file.id === newFile.id
              ? {
                  ...file,
                  status: 'failed',
                  progress: 0,
                  error: 'Unable to get upload permission',
                  uploadMessage: 'Upload failed',
                }
              : file,
          ),
        )
        global.showToast(
          `Unable to upload outstanding documents at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFiles((prev) =>
        prev.map((file) =>
          file.id === newFile.id
            ? {
                ...file,
                status: 'uploading',
                progress: 50,
                uploadMessage: 'Uploading to cloud storage...',
              }
            : file,
        ),
      )

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': fileJson.contentType,
        },
        body: fileJson.rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFiles((prev) =>
          prev.map((file) =>
            file.id === newFile.id
              ? {
                  ...file,
                  status: 'completed',
                  progress: 100,
                  objectKey: getS3SignedUrl.objectKey,
                  uploadMessage: 'Upload complete',
                }
              : file,
          ),
        )
        setError('')
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (uploadError) {
      // Update file status to failed
      setFiles((prev) =>
        prev.map((file) =>
          file.id === newFile.id
            ? {
                ...file,
                status: 'failed',
                progress: 0,
                error: uploadError.message || 'Upload failed',
                uploadMessage: 'Upload failed',
              }
            : file,
        ),
      )
      global.showToast(
        `Unable to upload outstanding documents at this time. Please try again later.`,
        { type: 'error' },
      )
    } finally {
      setCurrentDocumentType({})
      setSelectedDocumentType({})
      setShowAnotherFile(false)
    }
  }

  const removeFile = async (fileId, fileDetails) => {
    setRemovingFileId(fileId)

    try {
      if (fileDetails.objectKey) {
        await deleteFileFromS3({
          objectKey: fileDetails.objectKey,
          BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
        })
      }

      const fileIndex = files.findIndex((file) => file.id === fileId)
      setFiles((prev) => prev.filter((file) => file.id !== fileId))

      // Adjust currentCardIndex when removing files
      if (fileIndex <= currentCardIndex && currentCardIndex > 0) {
        setCurrentCardIndex(currentCardIndex - 1)
      } else if (files.length <= 1) {
        setCurrentCardIndex(0)
      }

      if (files.length <= 1) {
        setError('')
      }
      if (files.length === 0) {
        setShowAnotherFile(false)
      }
    } catch (removeError) {
      // Optionally show error toast
      global.showToast('Unable to remove file. Please try again.', {
        type: 'error',
      })
    } finally {
      setRemovingFileId(null)
    }
  }

  const handleSaveDocument = async () => {
    setIsFileSaving(true)

    try {
      // Update all files to uploading status
      setFiles((prev) =>
        prev.map((file) => ({ ...file, status: 'uploading', progress: 50 })),
      )

      // Prepare files array for single API call - use individual file document types
      const filesArray = files.map((fileItem) => ({
        ...fileItem,
        documentType: fileItem.documentType?.label || fileItem.documentType,
      }))

      // Send all files in a single API call
      await handleSave({
        files: filesArray,
        documentType: null, // No longer using global document type
        taskId,
        taskDetails,
      })

      // Mark all files as completed
      setFiles((prev) =>
        prev.map((file) => ({ ...file, status: 'completed', progress: 100 })),
      )

      // Close modal after successful upload
      setTimeout(() => {
        handleClose()
        resetModal()
      }, 1000)
    } catch (uploadError) {
      // Mark all files as failed
      setFiles((prev) =>
        prev.map((file) => ({
          ...file,
          status: 'failed',
          progress: 0,
          error: uploadError.message || 'Upload failed',
        })),
      )
      setError('Files failed to upload. Please try again.')
    } finally {
      setIsFileSaving(false)
    }
  }

  const resetModal = () => {
    setSelectedDocumentType({})
    setFiles([])
    setIsAgreed(false)
    setError('')
    setCurrentCardIndex(0)
    setCurrentDocumentType({})
    setRemovingFileId(null)
    setShouldScrollToEnd(false)
    setShowAnotherFile(false)
  }

  const canCTADisable = () => {
    // Check if all files have document types assigned
    const allFilesHaveDocumentType = files.every(
      (file) =>
        file.documentType && (file.documentType.label || file.documentType),
    )

    // Check if any files have failed uploads
    const hasFailedFiles = files.some((file) => file.status === 'failed')

    // Check if any files are currently uploading
    const hasUploadingFiles = files.some(
      (file) => file.status === 'preparing' || file.status === 'uploading',
    )

    return (
      files.length === 0 ||
      !allFilesHaveDocumentType ||
      hasFailedFiles ||
      hasUploadingFiles ||
      !!error ||
      !isAgreed ||
      isFileSaving
    )
  }

  const getFileTypeImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  const getFileStatusIcon = (file) => {
    switch (file.status) {
      case 'preparing':
        return <Loader size={15} />
      case 'uploading':
        return <Loader size={15} />
      case 'completed':
        return (
          <Icon name="Check" height={16} width={16} color={colors.success} />
        )
      case 'failed':
        return (
          <Icon
            name="AlertCircle"
            height={15}
            width={15}
            color={colors.onAlert}
          />
        )
      default:
        return null
    }
  }

  const addMoreFiles = (documentTypeForFiles = null) => {
    // Check if document type is selected (for initial upload or when adding more files)
    const docType =
      documentTypeForFiles || currentDocumentType || selectedDocumentType
    if (!docType || !docType.label) {
      setError('Please select a document type first')
      return
    }

    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.accept = allowedFileTypes
      .map((type) => `.${type.toLowerCase()}`)
      .join(',')

    input.onchange = async (e) => {
      const selectedFiles = Array.from(e.target.files)

      // Check if adding these files would exceed the limit
      if (files.length + selectedFiles.length > maxFiles) {
        setError(`Maximum ${maxFiles} files allowed`)
        return
      }

      // Check file name character limits
      const longNameFile = selectedFiles.find((file) => {
        const baseName = file.name.split('.').slice(0, -1).join('.')
        return baseName.length > 70
      })

      if (longNameFile) {
        setError(
          `File "${longNameFile.name}" name must be 70 characters or less`,
        )
        return
      }

      // // Check for duplicate file names with existing files
      // const duplicateWithExisting = selectedFiles.find((file) =>
      //   existingFileNames.includes(file.name),
      // )

      // if (duplicateWithExisting) {
      //   setError(
      //     `File "${duplicateWithExisting.name}" has already been selected`,
      //   )
      //   return
      // }

      // Check for duplicates within the current selection
      const selectedFileNames = selectedFiles.map((file) => file.name)
      const uniqueNames = new Set(selectedFileNames)

      if (selectedFileNames.length !== uniqueNames.size) {
        const duplicateInSelection = selectedFileNames.find(
          (name, index) => selectedFileNames.indexOf(name) !== index,
        )
        setError(
          `Duplicate file "${duplicateInSelection}" in current selection`,
        )
        return
      }

      // Process all files in parallel
      const filePromises = selectedFiles.map(async (selectedFile) => {
        const base64 = await new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result)
          reader.readAsDataURL(selectedFile)
        })

        const fileJson = {
          name: selectedFile.name,
          size: selectedFile.size,
          type: selectedFile.type.split('/')[1],
          contentType: selectedFile.type,
          rawFile: selectedFile,
        }

        return {
          fileJson,
          file: base64.split(',')[1],
        }
      })

      try {
        const processedFiles = await Promise.all(filePromises)
        processedFiles.forEach(async ({ fileJson, file }) => {
          await handleFileSelect({
            fileJson,
            file,
            documentType: docType,
          })
        })
      } catch (processError) {
        setError('Error processing files. Please try again.')
      }
    }

    input.click()
  }

  const renderFileItem = ({ item: file, index }) => {
    const isUploading =
      file.status === 'preparing' || file.status === 'uploading'
    const isFailed = file.status === 'failed'
    const isCurrentCard = index === currentCardIndex
    const isRemoving = removingFileId === file.id

    return (
      <View style={[styles.fileRow, isCurrentCard && styles.fileRowActive]}>
        <View style={styles.cardHeader}>
          {/* File icon and basic info */}
          <View style={styles.fileIconContainer}>
            <Image
              source={getFileTypeImage(file.name)}
              style={styles.fileIcon}
            />
          </View>

          <View style={styles.fileBasicInfo}>
            <Text variant="body2" style={styles.fileName} numberOfLines={1}>
              {file.name}
            </Text>
            <Text variant="display4" style={styles.documentTypeText}>
              {file.documentType?.label || file.documentType}
            </Text>
          </View>

          {/* Status icon and action buttons */}
          <View style={styles.cardActions}>
            <View style={styles.statusIconContainer}>
              {getFileStatusIcon(file)}
            </View>

            {isRemoving ? (
              <View style={styles.actionButton}>
                <Loader size={16} />
              </View>
            ) : (
              <View style={styles.actionButtonsContainer}>
                {/* Show retry button for failed uploads */}
                {isFailed && !isUploading && (
                  <TouchableOpacity
                    style={styles.retryButton}
                    onPress={() => retryFileUpload(file)}
                    disabled={isRemoving}
                    accessibilityLabel="Retry upload"
                    accessibilityRole="button"
                  >
                    <Icon
                      name="Refresh"
                      height={16}
                      width={16}
                      color={colors.primary}
                    />
                  </TouchableOpacity>
                )}
                {/* Delete button */}
                {!isUploading && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeFile(file.id, file)}
                    disabled={isRemoving}
                  >
                    <Icon
                      name="VisaTrash"
                      height={16}
                      width={16}
                      color={colors.primaryIconColor}
                    />
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>

        {/* Status messages and progress */}
        <View style={styles.cardFooter}>
          {/* Upload status message */}
          {(file.status === 'preparing' || file.status === 'uploading') && (
            <View style={styles.uploadingContainer}>
              <Text
                variant="caption"
                color={colors.primary}
                style={styles.uploadStatusText}
              >
                {file.uploadMessage}
              </Text>
              {/* Progress bar */}
              <View style={styles.progressContainer}>
                <View
                  style={[styles.progressBar, { width: `${file.progress}%` }]}
                />
              </View>
            </View>
          )}

          {/* Error message for failed uploads */}
          {isFailed && file.error && (
            <View style={styles.errorContainer}>
              <Text
                variant="caption"
                color={colors.onAlert}
                style={styles.errorText}
              >
                {file.error}
              </Text>
              <Text
                variant="caption"
                color={colors.primary}
                style={styles.retryHintText}
              >
                Use the retry button to upload again
              </Text>
            </View>
          )}
        </View>
      </View>
    )
  }

  const CARD_WIDTH_MOBILE = 302 // Card width (290) + gap (12) for mobile

  const handleScroll = (event) => {
    const scrollX = event.nativeEvent.contentOffset.x
    const newIndex = Math.round(scrollX / CARD_WIDTH_MOBILE)
    if (
      newIndex !== currentCardIndex &&
      newIndex >= 0 &&
      newIndex < files.length
    ) {
      setCurrentCardIndex(newIndex)
    }
  }

  const renderFileCarousel = () => {
    if (files.length === 0) return null

    const showLeftArrow = currentCardIndex > 0
    const showRightArrow = currentCardIndex < files.length - 1

    return (
      <View style={styles.carouselContainer}>
        {/* Scroll indicators for multiple files */}
        {files.length > 1 && (
          <View style={styles.scrollIndicatorContainer}>
            <Text
              variant="caption"
              style={styles.scrollHint}
              color={colors.primaryIconColor}
            >
              Swipe to view all files
            </Text>
            <View style={styles.arrowContainer}>
              {showLeftArrow && (
                <Icon
                  name="ArrowNarrowLeft"
                  height={16}
                  width={16}
                  color={colors.primary}
                  style={styles.scrollArrow}
                />
              )}
              {showRightArrow && (
                <Icon
                  name="ArrowNarrowRight"
                  height={16}
                  width={16}
                  color={colors.primary}
                  style={styles.scrollArrow}
                />
              )}
            </View>
          </View>
        )}

        {/* Horizontal scrollable list for document cards with visual scroll cues */}
        <View style={styles.scrollableContainer}>
          {/* Left fade indicator */}
          {files.length > 1 && showLeftArrow && (
            <View style={[styles.fadeIndicator, styles.fadeLeft]} />
          )}

          <FlatList
            ref={flatListRef}
            data={files}
            renderItem={({ item, index }) => renderFileItem({ item, index })}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.flatListContainer}
            style={styles.flatListStyle}
            decelerationRate="fast"
            snapToInterval={CARD_WIDTH_MOBILE}
            snapToAlignment="start"
            onScroll={handleScroll}
            scrollEventThrottle={16}
            getItemLayout={(data, index) => ({
              length: CARD_WIDTH_MOBILE,
              offset: CARD_WIDTH_MOBILE * index,
              index,
            })}
            onScrollToIndexFailed={() => {
              // Fallback: scroll to end if scrollToIndex fails
              setTimeout(() => {
                if (flatListRef.current) {
                  flatListRef.current.scrollToEnd({ animated: true })
                }
              }, 100)
            }}
          />

          {/* Right fade indicator */}
          {files.length > 1 && showRightArrow && (
            <View style={[styles.fadeIndicator, styles.fadeRight]} />
          )}
        </View>

        {/* Position indicator text */}
        {files.length > 1 && (
          <View style={styles.countIndicatorContainer}>
            <Text
              variant="caption"
              style={styles.positionIndicator}
              color={colors.primaryIconColor}
            >
              Showing {currentCardIndex + 1} of {files.length} files selected
            </Text>
          </View>
        )}
      </View>
    )
  }

  const getSheetHeight = () => {
    if (files.length > 0 && files.length < 5 && !showAnotherFile) {
      return 470
    }
    if (files.length > 0 && files.length < 5 && showAnotherFile) {
      return 520
    }

    return 450
  }

  const retryFileUpload = async (file) => {
    try {
      // Update status to show we're retrying
      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id
            ? {
                ...f,
                status: 'preparing',
                progress: 20,
                error: null,
                uploadMessage: 'Retrying upload...',
              }
            : f,
        ),
      )

      setShowAnotherFile(false)

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: file.contentType,
        documentType: file.documentType?.label || file.documentType,
        fileName: file.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === file.id
              ? {
                  ...f,
                  status: 'failed',
                  progress: 0,
                  error: 'Unable to get upload permission',
                  uploadMessage: 'Upload failed',
                }
              : f,
          ),
        )
        global.showToast(
          `Unable to retry upload at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id
            ? {
                ...f,
                status: 'uploading',
                progress: 50,
                uploadMessage: 'Uploading to cloud storage...',
              }
            : f,
        ),
      )

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.contentType,
        },
        body: file.rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFiles((prev) =>
          prev.map((f) =>
            f.id === file.id
              ? {
                  ...f,
                  status: 'completed',
                  progress: 100,
                  objectKey: getS3SignedUrl.objectKey,
                  uploadMessage: 'Upload complete',
                }
              : f,
          ),
        )
        // Clear any global errors if retry was successful
        setError('')
        global.showToast('File uploaded successfully!', { type: 'success' })
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (uploadError) {
      // Update file status to failed
      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id
            ? {
                ...f,
                status: 'failed',
                progress: 0,
                error: uploadError.message || 'Upload failed',
                uploadMessage: 'Upload failed',
              }
            : f,
        ),
      )
      global.showToast(
        `Unable to retry upload at this time. Please try again later.`,
        { type: 'error' },
      )
    } finally {
      setSelectedDocumentType({})
      setCurrentDocumentType({})
    }
  }

  return (
    <BottomSheet
      drawerHeight={getSheetHeight()}
      isVisible={visible}
      setIsVisible={setModalVisible}
      style={{
        backgroundColor: '#fff',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
      }}
    >
      <View style={styles.topdivider} />
      <View style={[styles.container, styles.modalOverlay]}>
        <View style={styles.card}>
          <View style={[styles.header, { paddingTop: 12 }]}>
            <Text
              style={{
                fontSize: 24,
                fontWeight: '700',
                color: colors.neutral,
              }}
              placeHolder="Text"
            >
              {title}
            </Text>

            <View style={[styles.uploadContainer, { paddingBottom: 0 }]}>
              <Text
                variant="caption"
                style={styles.uploadHint}
                color={colors.primaryIconColor}
              >
                * Upload {allowedFileTypes.join(', ')} up to{' '}
                {Math.round(maxFileSize / 1048576)}MB
              </Text>
            </View>
          </View>

          {/* File Upload Area - Matching main index.js structure exactly */}
          {files.length === 0 ? (
            <View style={{ width: '100%' }}>
              <Text
                variant="display4"
                color={colors.neutral}
                style={{ marginBottom: 8 }}
              >
                {t('FILE_UPLOAD.DOCUMENT_TYPE')}{' '}
                <Text variant="display4" color={colors.onAlert}>
                  *
                </Text>
              </Text>

              <View
                ref={dropDownRef}
                style={{
                  flexDirection: 'column',
                  gap: 16,
                }}
              >
                <DropDown
                  items={documentType}
                  toggleDropdown={toggleDropdown}
                  position={dropDownPosition}
                  dropdownWidth={dropdownWidth}
                  onPress={(selectedOption) => {
                    setSelectedDocumentType(selectedOption)
                    setCurrentDocumentType(selectedOption)
                  }}
                  label={t('DROP_DOWN.LABEL_SELECT')}
                  dropdownHeight={190}
                  style={{
                    margin: 0,
                    flex: 1,
                  }}
                  placeHolderColor={colors.fieldBorder}
                  value={selectedDocumentType}
                  disable={false}
                />
                <TouchableOpacity
                  onPress={() => addMoreFiles(currentDocumentType)}
                  disabled={!currentDocumentType || !currentDocumentType.label}
                  style={{
                    opacity:
                      !currentDocumentType || !currentDocumentType.label
                        ? 0.5
                        : 1,
                  }}
                >
                  <Text
                    variant="heading6"
                    color={colors.white}
                    style={{
                      borderRadius: 3,
                      borderColor: colors.primary,
                      borderWidth: 1,
                      color: colors.primary,
                      backgroundColor: '#ECF2FC',
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      textAlign: 'center',
                    }}
                  >
                    Upload File
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
          {files.length > 0 ? (
            <View style={styles.fileListContainer}>
              {renderFileCarousel()}

              <View style={styles.divider} />

              {/* Upload another file link */}
              {files.length < maxFiles && !isFileSaving && !showAnotherFile && (
                <TouchableOpacity
                  style={styles.uploadAnotherButton}
                  onPress={() => setShowAnotherFile(true)}
                >
                  <Text
                    variant="body2"
                    style={styles.uploadAnotherText}
                    color={colors.primary}
                  >
                    Upload another file +
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ) : null}
          {showAnotherFile && files.length > 0 && files.length < maxFiles ? (
            <View style={{ marginBottom: 12 }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  justifyContent: 'space-between',
                }}
              >
                <Text
                  variant="display4"
                  color={colors.neutral}
                  style={{ marginBottom: 8 }}
                >
                  {t('FILE_UPLOAD.DOCUMENT_TYPE')}{' '}
                  <Text variant="display4" color={colors.onAlert}>
                    *
                  </Text>
                </Text>
              </View>

              <View
                ref={dropDownRef}
                style={{
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  gap: 16,
                }}
              >
                <DropDown
                  items={documentType}
                  toggleDropdown={toggleDropdown}
                  position={dropDownPosition}
                  dropdownWidth={dropdownWidth}
                  onPress={(selectedOption) => {
                    setCurrentDocumentType(selectedOption)
                  }}
                  label={t('DROP_DOWN.LABEL_SELECT')}
                  dropdownHeight={190}
                  style={{
                    margin: 0,
                    flex: 1,
                  }}
                  placeHolderColor={colors.fieldBorder}
                  value={currentDocumentType}
                  disable={false}
                />
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 8,
                    width: '100%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => addMoreFiles(currentDocumentType)}
                    disabled={
                      !currentDocumentType || !currentDocumentType.label
                    }
                    style={{
                      opacity:
                        !currentDocumentType || !currentDocumentType.label
                          ? 0.5
                          : 1,
                      flex: 1,
                    }}
                  >
                    <Text
                      variant="heading6"
                      color={colors.white}
                      style={{
                        borderRadius: 3,
                        borderColor: colors.primary,
                        borderWidth: 1,
                        color: colors.primary,
                        backgroundColor: '#ECF2FC',
                        paddingVertical: 12,
                        paddingHorizontal: 16,
                        textAlign: 'center',
                      }}
                    >
                      Select File
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setShowAnotherFile(false)}>
                    <svg
                      width="45"
                      height="46"
                      viewBox="0 0 45 46"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clipPath="url(#clip0_3431_8134)">
                        <path
                          d="M38.4444 1.94141H6.55556C4.03959 1.94141 2 4.03074 2 6.60807V39.2747C2 41.8521 4.03959 43.9414 6.55556 43.9414H38.4444C40.9604 43.9414 43 41.8521 43 39.2747V6.60807C43 4.03074 40.9604 1.94141 38.4444 1.94141Z"
                          stroke="#0B5CD7"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M16.875 17.2539L28.125 28.5039"
                          stroke="#0B5CD7"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M28.125 17.2539L16.875 28.5039"
                          stroke="#0B5CD7"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_3431_8134">
                          <rect
                            width="45"
                            height="45"
                            fill="white"
                            transform="translate(0 0.378906)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ) : null}

          {/* Checkbox for agreement */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'flex-start',
              marginVertical: files.length === 0 ? 16 : 12,
            }}
          >
            <TouchableOpacity
              style={{
                width: 20,
                height: 20,
                borderWidth: 1,
                borderColor: colors.fieldBorder,
                borderRadius: 4,
                marginRight: 12,
                marginTop: 2,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: isAgreed ? colors.primary : 'transparent',
              }}
              onPress={() => setIsAgreed(!isAgreed)}
            >
              {isAgreed && (
                <Icon name="Check" height={12} width={12} color="#FFFFFF" />
              )}
            </TouchableOpacity>
            <Text
              variant="display4"
              color={colors.neutral}
              style={{ flex: 1, lineHeight: 20 }}
            >
              I confirm all required documents have been uploaded. I understand
              I cannot upload further documents for this task after saving
            </Text>
          </View>

          {error ? (
            <Text
              variant="display4"
              color={colors.onAlert}
              style={{ marginVertical: 12 }}
            >
              {error}
            </Text>
          ) : null}

          <View style={styles.buttonContainer}>
            {isFileSaving ? (
              <View style={{ alignItems: 'center', paddingVertical: 20 }}>
                <Loader size={20} />
                <Text variant="body2" style={{ marginTop: 10 }}>
                  Uploading files...
                </Text>
              </View>
            ) : (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'column',
                  gap: 16,
                }}
              >
                <Button
                  label={t('BUTTON.SAVE')}
                  buttonColor={colors.primary}
                  onPress={() => handleSaveDocument()}
                  disable={canCTADisable()}
                  labelColors="#FFFF"
                  labelvariant="heading5"
                  buttonStyle={{
                    flex: 1,
                    borderRadius: 4,
                    paddingHorizontal: 83.5,
                    paddingVertical: 10,
                  }}
                  labelStyle={{
                    textTransform: 'uppercase',
                    fontWeight: '700',
                  }}
                />
                <Button
                  label={t('BUTTON.CANCEL')}
                  buttonColor="#B6CFF3"
                  onPress={() => {
                    files.forEach(async (file) => {
                      if (file.objectKey) {
                        await deleteFileFromS3({
                          objectKey: file.objectKey,
                          BusinessUnitFilter__c:
                            opportunityDetails.BusinessUnitFilter__c,
                        })
                      }
                    })
                    handleClose()
                    resetModal()
                  }}
                  appearance="outline"
                  labelColors={colors.primary}
                  buttonStyle={{
                    flex: 1,
                    borderRadius: 4,
                    paddingHorizontal: 83.5,
                    paddingVertical: 10,
                  }}
                  labelStyle={{ textTransform: 'uppercase' }}
                />
              </View>
            )}
          </View>
        </View>
      </View>
    </BottomSheet>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    width: '100%',
    paddingHorizontal: 8,
  },
  card: {
    width: '100%',
    borderRadius: 15,
  },
  header: {
    position: 'relative',
  },
  buttonContainer: {
    flexDirection: 'column',
  },
  overlay: {
    flex: 1,
  },
  topdivider: {
    borderWidth: 2,
    borderColor: 'rgba(212, 212, 216, 1)',
    borderStyle: 'solid',
    marginHorizontal: 80,
    borderRadius: 3.5,
    marginBottom: 15,
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginTop: 6,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0B5CD7',
    borderRadius: 2,
    transition: 'width 0.3s ease',
  },
  // Screenshot-based Design Styles - Mobile Optimized
  uploadContainer: {
    marginTop: 0,
    marginBottom: 12,
    alignItems: 'start',
    paddingBottom: 20,
    paddingTop: 8,
  },
  uploadButton: {
    backgroundColor: '#4F46E5',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
    marginBottom: 12,
  },
  uploadButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    padding: 3,
  },
  uploadHint: {
    fontSize: 12,
    textAlign: 'center',
  },
  fileListContainer: {
    marginTop: 6,
    marginBottom: 10,
  },
  fileRow: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 10,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    width: 290, // Fixed width for horizontal scrolling
    minWidth: 290,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileIconContainer: {
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fileIcon: {
    height: 20,
    width: 20,
  },
  fileBasicInfo: {
    flex: 1,
    marginRight: 8,
  },
  fileName: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 4,
  },
  documentTypeLabel: {
    fontSize: 11,
    marginBottom: 4,
  },
  documentTypeText: {
    fontSize: 12,
    color: '#666666',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusIconContainer: {
    marginRight: 4,
  },
  cardFooter: {
    minHeight: 'auto',
  },
  uploadingContainer: {
    marginTop: 4,
  },
  errorContainer: {
    marginTop: 4,
    padding: 6,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  actionButton: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  retryButton: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(11, 92, 215, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(11, 92, 215, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  retryHintText: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  divider: {
    borderBottomWidth: 1,
    borderColor: '#162447',
    opacity: 0.1,
  },
  errorText: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 2,
  },
  uploadStatusText: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 4,
  },
  fileActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  removeButton: {
    padding: 4,
  },
  uploadAnotherButton: {
    alignItems: 'center',
    paddingTop: 32,
  },
  uploadAnotherText: {
    fontSize: 14,
    fontWeight: '700',
  },
  // Carousel styles
  carouselContainer: {
    marginBottom: 8,
  },
  scrollIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  scrollHint: {
    fontSize: 13,
    fontStyle: 'italic',
  },
  arrowContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  scrollArrow: {
    opacity: 0.8,
  },
  scrollableContainer: {
    position: 'relative',
  },
  fadeIndicator: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: 20,
    zIndex: 1,
    pointerEvents: 'none',
  },
  fadeLeft: {
    left: 0,
    background: 'linear-gradient(to right, rgba(255,255,255,0.9), transparent)',
  },
  fadeRight: {
    right: 0,
    background: 'linear-gradient(to left, rgba(255,255,255,0.9), transparent)',
  },
  flatListStyle: {},
  flatListContainer: {
    paddingHorizontal: 4,
    gap: 12,
  },
  countIndicatorContainer: {
    alignItems: 'start',
    marginLeft: 4,
  },
  countIndicator: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Enhanced carousel styles
  fileRowActive: {
    borderWidth: 2,
    borderColor: '#0B5CD7',
    shadowColor: '#0B5CD7',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  positionIndicator: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
})

export default MultiFileUploadModalMobile
