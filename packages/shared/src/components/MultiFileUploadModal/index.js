import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native'
import React, { useState, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, DropDown, Loader, Text } from '@libs/components'

import { Icon } from '@app-hero/native-icons'
import {
  PdfImage,
  PngImage,
  JpegImage,
  JpgImage,
  MiscImage,
} from '@apphero/assets'
import { useTheme } from '@libs/theme'
import { BlurView } from 'expo-blur'
import MultiFileUploadModalMobile from './index.mobile'
import { deleteFileFromS3, generateS3SignedUrl } from '../../api'

const MultiFileUploadModal = ({
  title = '',
  visible = false,
  toggleDropdown = () => {},
  handleSave = async () => {},
  handleClose = () => {},
  dropDownRef,
  dropDownPosition = {},
  dropdownWidth = '',
  documentType = [],
  selectedDropDownValue,
  taskId,
  setModalVisible,
  taskDetails,
  maxFiles = 5,
  maxFileSize = 5242880, // 5MB in bytes
  allowedFileTypes = ['PDF', 'PNG', 'JPG', 'JPEG'],
  opportunityDetails,
}) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const [files, setFiles] = useState([])
  const [error, setError] = useState()
  const [selectedDocumentType, setSelectedDocumentType] = useState({})
  const [isFileSaving, setIsFileSaving] = useState()
  const [isAgreed, setIsAgreed] = useState(false)
  const [showAnotherFile, setShowAnotherFile] = useState(false)
  const [currentDocumentType, setCurrentDocumentType] = useState({})
  const scrollViewRef = useRef(null)
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [, setScrollViewWidth] = useState(0)
  const [isNavigating, setIsNavigating] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)
  const [removingFileId, setRemovingFileId] = useState(null)
  const [shouldScrollToEnd, setShouldScrollToEnd] = useState(false)

  const CARD_WIDTH = 422 // Card width (410) + gap (12)

  // Effect to handle auto-scrolling to newly added files
  useEffect(() => {
    if (shouldScrollToEnd && files.length > 0) {
      const lastIndex = files.length - 1

      // Don't scroll if already navigating/scrolling
      if (!isNavigating && !isScrolling) {
        setCurrentCardIndex(lastIndex)

        setTimeout(() => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({
              x: lastIndex * CARD_WIDTH,
              animated: true,
            })
          }
        }, 100)
      }

      setShouldScrollToEnd(false)
    }
  }, [files.length, shouldScrollToEnd, isNavigating, isScrolling])

  const validateFile = (file) => {
    const errors = []

    // Check file size
    if (file.size > maxFileSize) {
      errors.push(`File size must be less than 5MB`)
    }

    // Check file type
    const fileExtension = file.name.split('.').pop().toUpperCase()
    if (!allowedFileTypes.includes(fileExtension)) {
      errors.push(`File type must be one of: ${allowedFileTypes.join(', ')}`)
    }

    // Check file name length
    const baseName = file.name.split('.').slice(0, -1).join('.')
    if (baseName.length > 70) {
      errors.push('File name must be 70 characters or less')
    }

    return errors
  }

  const handleFileSelect = async ({
    fileJson,
    error: fileError,
    file: selectedFile,
    documentType: fileDocumentType,
  }) => {
    if (fileError) {
      setError(fileError)
      return
    }

    // Check if we've reached the maximum number of files
    if (files.length >= maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`)
      return
    }

    // Validate the file
    const validationErrors = validateFile(fileJson)
    if (validationErrors.length > 0) {
      setError(validationErrors[0])
      return
    }

    const newFile = {
      ...fileJson,
      file: selectedFile,
      id: Date.now() + Math.random(), // Unique identifier
      status: 'preparing', // preparing, uploading, completed, failed
      progress: 0,
      error: null,
      documentType: fileDocumentType || currentDocumentType,
      uploadMessage: 'Preparing upload...',
    }

    // Add file to list immediately to show preparation status
    setFiles((prev) => [...prev, newFile])

    // Trigger auto-scroll to the newly added file immediately
    setShouldScrollToEnd(true)

    try {
      // Update status to show we're getting the signed URL
      setFiles((prev) =>
        prev.map((file) =>
          file.id === newFile.id
            ? {
                ...file,
                status: 'preparing',
                progress: 20,
                uploadMessage: 'Getting upload permission...',
              }
            : file,
        ),
      )

      setShowAnotherFile(false)

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: fileJson.contentType,
        documentType: fileDocumentType.label,
        fileName: fileJson.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFiles((prev) =>
          prev.map((file) =>
            file.id === newFile.id
              ? {
                  ...file,
                  status: 'failed',
                  progress: 0,
                  error: 'Unable to get upload permission',
                  uploadMessage: 'Upload failed',
                }
              : file,
          ),
        )
        global.showToast(
          `Unable to upload outstanding documents at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFiles((prev) =>
        prev.map((file) =>
          file.id === newFile.id
            ? {
                ...file,
                status: 'uploading',
                progress: 50,
                uploadMessage: 'Uploading to cloud storage...',
              }
            : file,
        ),
      )

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': fileJson.contentType,
        },
        body: fileJson.rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFiles((prev) =>
          prev.map((file) =>
            file.id === newFile.id
              ? {
                  ...file,
                  status: 'completed',
                  progress: 100,
                  objectKey: getS3SignedUrl.objectKey,
                  uploadMessage: 'Upload complete',
                }
              : file,
          ),
        )
        setError('')
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (uploadError) {
      // Update file status to failed
      setFiles((prev) =>
        prev.map((file) =>
          file.id === newFile.id
            ? {
                ...file,
                status: 'failed',
                progress: 0,
                error: uploadError.message || 'Upload failed',
                uploadMessage: 'Upload failed',
              }
            : file,
        ),
      )
      global.showToast(
        `Unable to upload outstanding documents at this time. Please try again later.`,
        { type: 'error' },
      )
    } finally {
      setCurrentDocumentType({})
      setSelectedDocumentType({})
      setShowAnotherFile(false)
    }
  }

  const removeFile = async (fileId, fileDetails) => {
    setRemovingFileId(fileId)

    try {
      if (fileDetails.objectKey) {
        await deleteFileFromS3({
          objectKey: fileDetails.objectKey,
          BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
        })
      }

      const fileIndex = files.findIndex((file) => file.id === fileId)
      setFiles((prev) => prev.filter((file) => file.id !== fileId))

      // Adjust currentCardIndex when removing files
      if (fileIndex <= currentCardIndex && currentCardIndex > 0) {
        setCurrentCardIndex(currentCardIndex - 1)
      } else if (files.length <= 1) {
        setCurrentCardIndex(0)
      }

      if (files.length <= 1) {
        setError('')
      }
      if (files.length === 0) {
        setShowAnotherFile(false)
      }
    } catch (removeError) {
      // Optionally show error toast
      global.showToast('Unable to remove file. Please try again.', {
        type: 'error',
      })
    } finally {
      setRemovingFileId(null)
    }
  }

  const handleSaveDocument = async () => {
    setIsFileSaving(true)

    try {
      // Update all files to uploading status
      setFiles((prev) =>
        prev.map((file) => ({ ...file, status: 'uploading', progress: 50 })),
      )

      // Prepare files array for single API call - use individual file document types
      const filesArray = files.map((fileItem) => ({
        ...fileItem,
        documentType: fileItem.documentType?.label || fileItem.documentType,
      }))

      // Send all files in a single API call
      await handleSave({
        files: filesArray,
        documentType: null, // No longer using global document type
        taskId,
        taskDetails,
      })

      // Mark all files as completed
      setFiles((prev) =>
        prev.map((file) => ({ ...file, status: 'completed', progress: 100 })),
      )

      // Close modal after successful upload
      setTimeout(() => {
        handleClose()
        resetModal()
      }, 1000)
    } catch (uploadError) {
      // Mark all files as failed
      setFiles((prev) =>
        prev.map((file) => ({
          ...file,
          status: 'failed',
          progress: 0,
          error: uploadError.message || 'Upload failed',
        })),
      )
      setError('Files failed to upload. Please try again.')
    } finally {
      setIsFileSaving(false)
    }
  }

  const resetModal = () => {
    setSelectedDocumentType({})
    setFiles([])
    setIsAgreed(false)
    setError('')
    setCurrentCardIndex(0)
    setIsNavigating(false)
    setIsScrolling(false)
    setCurrentDocumentType({})
    setRemovingFileId(null)
    setShouldScrollToEnd(false)
    setShowAnotherFile(false)
  }

  const scrollLeft = () => {
    if (
      scrollViewRef.current &&
      currentCardIndex > 0 &&
      !isNavigating &&
      !isScrolling
    ) {
      const newIndex = currentCardIndex - 1
      setIsNavigating(true)
      setCurrentCardIndex(newIndex)

      // Use smooth scrolling with consistent timing
      scrollViewRef.current.scrollTo({
        x: newIndex * CARD_WIDTH,
        animated: true,
      })

      // Reset navigation flag after a reasonable animation duration
      setTimeout(() => {
        setIsNavigating(false)
      }, 300)
    }
  }

  const scrollRight = () => {
    if (
      scrollViewRef.current &&
      currentCardIndex < files.length - 1 &&
      !isNavigating &&
      !isScrolling
    ) {
      const newIndex = currentCardIndex + 1
      setIsNavigating(true)
      setCurrentCardIndex(newIndex)

      // Use smooth scrolling with consistent timing
      scrollViewRef.current.scrollTo({
        x: newIndex * CARD_WIDTH,
        animated: true,
      })

      // Reset navigation flag after a reasonable animation duration
      setTimeout(() => {
        setIsNavigating(false)
      }, 300)
    }
  }

  const handleScroll = (event) => {
    // Minimal scroll handling - let native behavior handle most of the work
    // Only update index during smooth scrolling, not during momentum or navigation
    if (!isNavigating && !isScrolling) {
      const scrollX = event.nativeEvent.contentOffset.x
      const newIndex = Math.round(scrollX / CARD_WIDTH)
      const clampedIndex = Math.max(0, Math.min(newIndex, files.length - 1))

      // Only update if we're close to a card boundary to avoid flickering
      const distanceToCard = Math.abs(scrollX - clampedIndex * CARD_WIDTH)
      if (
        distanceToCard < CARD_WIDTH * 0.1 &&
        clampedIndex !== currentCardIndex
      ) {
        setCurrentCardIndex(clampedIndex)
      }
    }
  }

  const handleScrollEndDrag = (event) => {
    // Handle when user finishes dragging (before momentum starts)
    if (!isNavigating && files.length > 0) {
      const scrollX = event.nativeEvent.contentOffset.x
      const newIndex = Math.round(scrollX / CARD_WIDTH)
      const clampedIndex = Math.max(0, Math.min(newIndex, files.length - 1))

      // Update index immediately when drag ends for better responsiveness
      if (clampedIndex !== currentCardIndex) {
        setCurrentCardIndex(clampedIndex)
      }
    }
  }

  const handleScrollEnd = (event) => {
    // Update scroll state and current card index when momentum scrolling ends
    setIsScrolling(false)

    if (!isNavigating && files.length > 0) {
      const scrollX = event.nativeEvent.contentOffset.x

      // Calculate the current card index based on final scroll position
      // The native snapToInterval should handle the snapping, we just track the index
      const newIndex = Math.round(scrollX / CARD_WIDTH)
      const clampedIndex = Math.max(0, Math.min(newIndex, files.length - 1))

      // Update the current card index if it has changed
      if (clampedIndex !== currentCardIndex) {
        setCurrentCardIndex(clampedIndex)
      }
    }
  }

  const handleScrollBeginDrag = () => {
    // Mark that user is actively scrolling
    setIsScrolling(true)

    // Cancel any ongoing button navigation to allow manual scrolling
    if (isNavigating) {
      setIsNavigating(false)
    }
  }

  const canCTADisable = () => {
    // Check if all files have document types assigned
    const allFilesHaveDocumentType = files.every(
      (file) =>
        file.documentType && (file.documentType.label || file.documentType),
    )

    // Check if any files have failed uploads
    const hasFailedFiles = files.some((file) => file.status === 'failed')

    // Check if any files are currently uploading
    const hasUploadingFiles = files.some(
      (file) => file.status === 'preparing' || file.status === 'uploading',
    )

    return (
      files.length === 0 ||
      !allFilesHaveDocumentType ||
      hasFailedFiles ||
      hasUploadingFiles ||
      !!error ||
      !isAgreed ||
      isFileSaving
    )
  }

  const getFileTypeImage = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      case 'pdf':
        return PdfImage
      case 'png':
        return PngImage
      case 'jpg':
        return JpgImage
      case 'jpeg':
        return JpegImage
      default:
        return MiscImage
    }
  }

  const getFileStatusIcon = (file) => {
    switch (file.status) {
      case 'preparing':
        return <Loader size={15} />
      case 'uploading':
        return <Loader size={15} />
      case 'completed':
        return (
          <Icon name="Check" height={16} width={16} color={colors.success} />
        )
      case 'failed':
        return (
          <Icon
            name="AlertCircle"
            height={15}
            width={15}
            color={colors.onAlert}
          />
        )
      default:
        return null
    }
  }

  if (window.innerWidth < 650) {
    return (
      <MultiFileUploadModalMobile
        title={title}
        visible={visible}
        toggleDropdown={toggleDropdown}
        handleSave={handleSave}
        handleClose={handleClose}
        dropDownPosition={dropDownPosition}
        dropDownRef={dropDownRef}
        dropdownWidth={dropdownWidth}
        documentType={documentType}
        selectedDropDownValue={selectedDropDownValue}
        taskId={taskId}
        setModalVisible={setModalVisible}
        taskDetails={taskDetails}
        maxFiles={maxFiles}
        maxFileSize={maxFileSize}
        allowedFileTypes={allowedFileTypes}
        opportunityDetails={opportunityDetails}
      />
    )
  }

  const addMoreFiles = (documentTypeForFiles = null) => {
    // Check if document type is selected (for initial upload or when adding more files)
    const docType =
      documentTypeForFiles || currentDocumentType || selectedDocumentType
    if (!docType || !docType.label) {
      setError('Please select a document type first')
      return
    }

    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.accept = allowedFileTypes
      .map((type) => `.${type.toLowerCase()}`)
      .join(',')

    input.onchange = async (e) => {
      const selectedFiles = Array.from(e.target.files)

      // Check if adding these files would exceed the limit
      if (files.length + selectedFiles.length > maxFiles) {
        setError(`Maximum ${maxFiles} files allowed`)
        return
      }

      // Check file name character limits
      const longNameFile = selectedFiles.find((file) => {
        const baseName = file.name.split('.').slice(0, -1).join('.')
        return baseName.length > 70
      })

      if (longNameFile) {
        setError(
          `File "${longNameFile.name}" name must be 70 characters or less`,
        )
        return
      }

      // Check for duplicates within the current selection
      const selectedFileNames = selectedFiles.map((file) => file.name)
      const uniqueNames = new Set(selectedFileNames)

      if (selectedFileNames.length !== uniqueNames.size) {
        const duplicateInSelection = selectedFileNames.find(
          (name, index) => selectedFileNames.indexOf(name) !== index,
        )
        setError(
          `Duplicate file "${duplicateInSelection}" in current selection`,
        )
        return
      }

      // Process all files in parallel
      const filePromises = selectedFiles.map(async (selectedFile) => {
        const base64 = await new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result)
          reader.readAsDataURL(selectedFile)
        })

        const fileJson = {
          name: selectedFile.name,
          size: selectedFile.size,
          type: selectedFile.type.split('/')[1],
          contentType: selectedFile.type,
          rawFile: selectedFile,
        }

        return {
          fileJson,
          file: base64.split(',')[1],
        }
      })

      try {
        const processedFiles = await Promise.all(filePromises)
        processedFiles.forEach(async ({ fileJson, file }) => {
          await handleFileSelect({
            fileJson,
            file,
            documentType: docType,
          })
        })
      } catch (processError) {
        setError('Error processing files. Please try again.')
      }
    }

    input.click()
  }

  const renderFileCarousel = () => {
    if (files.length === 0) return null

    return (
      <View>
        {/* Horizontal scrollable container for document cards */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
          style={styles.scrollView}
          decelerationRate="normal"
          snapToInterval={CARD_WIDTH}
          snapToAlignment="start"
          onScroll={handleScroll}
          onMomentumScrollEnd={handleScrollEnd}
          onScrollEndDrag={handleScrollEndDrag}
          onScrollBeginDrag={handleScrollBeginDrag}
          scrollEventThrottle={8}
          pagingEnabled={false}
          bounces={false}
          directionalLockEnabled
          onLayout={(event) => {
            setScrollViewWidth(event.nativeEvent.layout.width)
          }}
        >
          {files.map((file, index) => {
            const isUploading =
              file.status === 'preparing' || file.status === 'uploading'
            const isFailed = file.status === 'failed'
            const isCurrentCard = index === currentCardIndex
            const isRemoving = removingFileId === file.id

            return (
              <View
                key={file.id}
                style={[
                  styles.documentCard,
                  isCurrentCard &&
                    files.length > 1 &&
                    styles.documentCardActive,
                  isRemoving && styles.documentCardRemoving,
                ]}
              >
                <View style={styles.cardHeader}>
                  {/* File icon and basic info */}
                  <View style={styles.fileIconContainer}>
                    <Image
                      source={getFileTypeImage(file.name)}
                      style={styles.documentIcon}
                    />
                  </View>

                  <View style={styles.fileBasicInfo}>
                    <Text
                      variant="body2"
                      style={styles.documentName}
                      numberOfLines={1}
                    >
                      {file.name}
                    </Text>
                    <Text variant="display4" style={styles.documentType}>
                      {file.documentType?.label}
                    </Text>
                  </View>

                  {/* Status icon and action buttons */}
                  <View style={styles.cardActions}>
                    <View style={styles.statusIconContainer}>
                      {getFileStatusIcon(file)}
                    </View>

                    {isRemoving ? (
                      <View style={styles.actionButton}>
                        <Loader size={16} />
                      </View>
                    ) : (
                      <View style={styles.actionButtonsContainer}>
                        {/* Show retry button for failed uploads */}
                        {isFailed && !isUploading && (
                          <TouchableOpacity
                            style={styles.retryButton}
                            onPress={() => retryFileUpload(file)}
                            disabled={isRemoving}
                            accessibilityLabel="Retry upload"
                            accessibilityRole="button"
                          >
                            <Icon
                              name="Refresh"
                              height={16}
                              width={16}
                              color={colors.primary}
                            />
                          </TouchableOpacity>
                        )}
                        {/* Delete button */}
                        {!isUploading && (
                          <TouchableOpacity
                            style={styles.deleteButton}
                            onPress={() => removeFile(file.id, file)}
                            disabled={isRemoving}
                          >
                            <Icon
                              name="VisaTrash"
                              height={18}
                              width={18}
                              color={colors.primaryIconColor}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    )}
                  </View>
                </View>

                {/* Status messages and progress */}
                <View style={styles.cardFooter}>
                  {/* Upload status message */}
                  {(file.status === 'preparing' ||
                    file.status === 'uploading') && (
                    <View style={styles.uploadingContainer}>
                      <Text
                        variant="caption"
                        color={colors.primary}
                        style={styles.uploadStatusText}
                      >
                        {file.uploadMessage}
                      </Text>
                      {/* Progress bar */}
                      <View style={styles.progressContainer}>
                        <View
                          style={[
                            styles.progressBar,
                            { width: `${file.progress}%` },
                          ]}
                        />
                      </View>
                    </View>
                  )}

                  {/* Error message for failed uploads */}
                  {isFailed && file.error && (
                    <View style={styles.errorContainer}>
                      <Text
                        variant="caption"
                        color={colors.onAlert}
                        style={styles.errorText}
                      >
                        {file.error}
                      </Text>
                      <Text
                        variant="caption"
                        color={colors.primary}
                        style={styles.retryHintText}
                      >
                        Use the retry button to upload again
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            )
          })}
        </ScrollView>

        {/* Position indicator and navigation controls */}
        {files.length > 1 && (
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              style={[
                styles.navButton,
                (currentCardIndex === 0 || isNavigating || isScrolling) &&
                  styles.navButtonDisabled,
              ]}
              onPress={scrollLeft}
              disabled={currentCardIndex === 0 || isNavigating || isScrolling}
            >
              <Icon
                name="ArrowUp"
                width={20}
                height={20}
                color={
                  currentCardIndex === 0 || isNavigating || isScrolling
                    ? colors.fieldBorder
                    : colors.primary
                }
                style={{ transform: [{ rotate: '-90deg' }] }}
              />
            </TouchableOpacity>

            {/* Position indicator */}
            <View style={styles.positionIndicatorContainer}>
              <Text
                variant="caption"
                style={styles.positionIndicator}
                color={colors.primaryIconColor}
              >
                {currentCardIndex + 1} of {files.length}
              </Text>

              {/* Dot indicators */}
              <View style={styles.dotContainer}>
                {files.map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.dot,
                      index === currentCardIndex && styles.dotActive,
                    ]}
                  />
                ))}
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.navButton,
                (currentCardIndex === files.length - 1 ||
                  isNavigating ||
                  isScrolling) &&
                  styles.navButtonDisabled,
              ]}
              onPress={scrollRight}
              disabled={
                currentCardIndex === files.length - 1 ||
                isNavigating ||
                isScrolling
              }
            >
              <Icon
                name="ArrowDown"
                width={20}
                height={20}
                color={
                  currentCardIndex === files.length - 1 ||
                  isNavigating ||
                  isScrolling
                    ? colors.fieldBorder
                    : colors.primary
                }
                style={{ transform: [{ rotate: '-90deg' }] }}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    )
  }

  const retryFileUpload = async (file) => {
    try {
      // Update status to show we're retrying
      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id
            ? {
                ...f,
                status: 'preparing',
                progress: 20,
                error: null,
                uploadMessage: 'Retrying upload...',
              }
            : f,
        ),
      )

      setShowAnotherFile(false)

      const getS3SignedUrl = await generateS3SignedUrl({
        applicationFormId: opportunityDetails.ApplicationFormId__c,
        contentType: file.contentType,
        documentType: file.documentType?.label || file.documentType,
        fileName: file.name,
        email: opportunityDetails.Account.PersonEmail || opportunityDetails.PK,
        BusinessUnitFilter__c: opportunityDetails.BusinessUnitFilter__c,
      })

      if (!getS3SignedUrl?.signedUrl) {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === file.id
              ? {
                  ...f,
                  status: 'failed',
                  progress: 0,
                  error: 'Unable to get upload permission',
                  uploadMessage: 'Upload failed',
                }
              : f,
          ),
        )
        global.showToast(
          `Unable to retry upload at this time. Please try again later.`,
          { type: 'error' },
        )
        return
      }

      // Update status to show we're uploading to S3
      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id
            ? {
                ...f,
                status: 'uploading',
                progress: 50,
                uploadMessage: 'Uploading to cloud storage...',
              }
            : f,
        ),
      )

      const presignedUrlResponse = await fetch(getS3SignedUrl.signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.contentType,
        },
        body: file.rawFile,
      })

      if (presignedUrlResponse.ok) {
        // Update to show upload completion
        setFiles((prev) =>
          prev.map((f) =>
            f.id === file.id
              ? {
                  ...f,
                  status: 'completed',
                  progress: 100,
                  objectKey: getS3SignedUrl.objectKey,
                  uploadMessage: 'Upload complete',
                }
              : f,
          ),
        )
        // Clear any global errors if retry was successful
        setError('')
        global.showToast('File uploaded successfully!', { type: 'success' })
      } else {
        throw new Error(
          `Upload failed with status: ${presignedUrlResponse.status}`,
        )
      }
    } catch (uploadError) {
      // Update file status to failed
      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id
            ? {
                ...f,
                status: 'failed',
                progress: 0,
                error: uploadError.message || 'Upload failed',
                uploadMessage: 'Upload failed',
              }
            : f,
        ),
      )
      global.showToast(
        `Unable to retry upload at this time. Please try again later.`,
        { type: 'error' },
      )
    } finally {
      setSelectedDocumentType({})
      setCurrentDocumentType({})
    }
  }

  return (
    <Modal
      visible={visible}
      onBackdropPress={() => {
        handleClose()
        resetModal()
      }}
      onRequestClose={() => {
        handleClose()
        resetModal()
      }}
      transparent
      animationType="none"
      style={{ flex: 1, zIndex: 200 }}
    >
      <BlurView intensity={80} style={styles.overlay}>
        <View style={[styles.container, styles.modalOverlay]}>
          <View style={styles.card}>
            <View style={[styles.header]}>
              <TouchableOpacity
                style={{
                  position: 'absolute',
                  right: window.innerWidth <= 440 ? -0 : -20,
                  top: window.innerWidth <= 440 ? -5 : -15,
                  zIndex: 10,
                }}
                onPress={() => {
                  handleClose()
                  resetModal()
                }}
              >
                <Icon name="Close" height={23} width={23} />
              </TouchableOpacity>
              <Text
                style={{
                  fontSize: 24,
                  fontWeight: '700',
                  color: colors.neutral,
                  paddingTop: 20,
                }}
                placeHolder="Text"
              >
                {title}
              </Text>

              <View
                style={[
                  styles.uploadContainer,
                  { paddingBottom: files.length > 0 ? 0 : 20 },
                ]}
              >
                <Text
                  variant="caption"
                  style={styles.uploadHint}
                  color={colors.primaryIconColor}
                >
                  * Upload {allowedFileTypes.join(', ')} up to{' '}
                  {Math.round(maxFileSize / 1048576)}MB
                </Text>
              </View>
            </View>

            {files.length === 0 ? (
              <View>
                <Text
                  variant="display4"
                  color={colors.neutral}
                  style={{ marginBottom: 8 }}
                >
                  {t('FILE_UPLOAD.DOCUMENT_TYPE')}{' '}
                  <Text variant="display4" color={colors.onAlert}>
                    *
                  </Text>
                </Text>

                <View
                  ref={dropDownRef}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: '16px',
                  }}
                >
                  <DropDown
                    items={documentType}
                    toggleDropdown={toggleDropdown}
                    position={dropDownPosition}
                    dropdownWidth={dropdownWidth - 120}
                    onPress={(selectedOption) => {
                      setSelectedDocumentType(selectedOption)
                      setCurrentDocumentType(selectedOption)
                    }}
                    label={t('DROP_DOWN.LABEL_SELECT')}
                    dropdownHeight={190}
                    style={{
                      margin: 0,
                      flex: 1,
                    }}
                    placeHolderColor={colors.fieldBorder}
                    value={selectedDocumentType}
                    disable={false}
                  />
                  <TouchableOpacity
                    onPress={() => addMoreFiles(currentDocumentType)}
                    disabled={
                      !currentDocumentType || !currentDocumentType.label
                    }
                    style={{
                      opacity:
                        !currentDocumentType || !currentDocumentType.label
                          ? 0.5
                          : 1,
                    }}
                  >
                    <Text
                      variant="heading6"
                      color={colors.white}
                      style={{
                        borderRadius: 3,
                        borderColor: colors.primary,
                        borderWidth: 1,
                        color: colors.primary,
                        backgroundColor: '#ECF2FC',
                        paddingVertical: 12,
                        paddingHorizontal: 16,
                      }}
                    >
                      Upload File
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}
            {files.length > 0 ? (
              <View style={styles.fileListContainer}>
                {renderFileCarousel()}

                <View style={styles.divider} />

                {/* Upload another file link */}
                {files.length < maxFiles && !isFileSaving && !showAnotherFile && (
                  <TouchableOpacity
                    style={styles.uploadAnotherButton}
                    onPress={() => setShowAnotherFile(true)}
                  >
                    <Text
                      variant="body2"
                      style={styles.uploadAnotherText}
                      color={colors.primary}
                    >
                      Upload another file +
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : null}
            {showAnotherFile && files.length > 0 && files.length < maxFiles ? (
              <View style={{ marginBottom: 12 }}>
                <Text
                  variant="display4"
                  color={colors.neutral}
                  style={{ marginBottom: 8 }}
                >
                  {t('FILE_UPLOAD.DOCUMENT_TYPE')}{' '}
                  <Text variant="display4" color={colors.onAlert}>
                    *
                  </Text>
                </Text>

                <View
                  ref={dropDownRef}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: '16px',
                  }}
                >
                  <DropDown
                    items={documentType}
                    toggleDropdown={toggleDropdown}
                    position={dropDownPosition}
                    dropdownWidth={dropdownWidth - 180}
                    onPress={(selectedOption) => {
                      setCurrentDocumentType(selectedOption)
                    }}
                    label={t('DROP_DOWN.LABEL_SELECT')}
                    dropdownHeight={190}
                    style={{
                      margin: 0,
                      flex: 1,
                    }}
                    placeHolderColor={colors.fieldBorder}
                    value={currentDocumentType}
                    disable={false}
                  />
                  <TouchableOpacity
                    onPress={() => addMoreFiles(currentDocumentType)}
                    disabled={
                      !currentDocumentType || !currentDocumentType.label
                    }
                    style={{
                      opacity:
                        !currentDocumentType || !currentDocumentType.label
                          ? 0.5
                          : 1,
                    }}
                  >
                    <Text
                      variant="heading6"
                      color={colors.white}
                      style={{
                        borderRadius: 3,
                        borderColor: colors.primary,
                        borderWidth: 1,
                        color: colors.primary,
                        backgroundColor: '#ECF2FC',
                        paddingVertical: 12,
                        paddingHorizontal: 16,
                      }}
                    >
                      Select File
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity onPress={() => setShowAnotherFile(false)}>
                    <svg
                      width="45"
                      height="46"
                      viewBox="0 0 45 46"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clipPath="url(#clip0_3431_8134)">
                        <path
                          d="M38.4444 1.94141H6.55556C4.03959 1.94141 2 4.03074 2 6.60807V39.2747C2 41.8521 4.03959 43.9414 6.55556 43.9414H38.4444C40.9604 43.9414 43 41.8521 43 39.2747V6.60807C43 4.03074 40.9604 1.94141 38.4444 1.94141Z"
                          stroke="#0B5CD7"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M16.875 17.2539L28.125 28.5039"
                          stroke="#0B5CD7"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M28.125 17.2539L16.875 28.5039"
                          stroke="#0B5CD7"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_3431_8134">
                          <rect
                            width="45"
                            height="45"
                            fill="white"
                            transform="translate(0 0.378906)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}
            {/* Checkbox for agreement */}
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginVertical: files.length === 0 ? 16 : 12,
              }}
            >
              <TouchableOpacity
                style={{
                  width: 20,
                  height: 20,
                  borderWidth: 1,
                  borderColor: colors.fieldBorder,
                  borderRadius: 4,
                  marginRight: 12,
                  marginTop: 2,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: isAgreed ? colors.primary : 'transparent',
                }}
                onPress={() => setIsAgreed(!isAgreed)}
              >
                {isAgreed && (
                  <Icon name="Check" height={12} width={12} color="#FFFFFF" />
                )}
              </TouchableOpacity>
              <Text
                variant="display4"
                color={colors.neutral}
                style={{ flex: 1, lineHeight: 20 }}
              >
                I confirm all required documents have been uploaded. I
                understand I cannot upload further documents for this task after
                saving
              </Text>
            </View>
            {error ? (
              <Text
                variant="display4"
                color={colors.onAlert}
                style={{ marginVertical: 12 }}
              >
                {error}
              </Text>
            ) : null}

            <View style={styles.buttonContainer}>
              {isFileSaving ? (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingVertical: 20,
                  }}
                >
                  <Loader size={20} />
                  <Text variant="body2" style={{ marginTop: 10 }}>
                    Uploading files...
                  </Text>
                </View>
              ) : (
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    gap: 16,
                    flexWrap: 'wrap',
                  }}
                >
                  <Button
                    label={t('BUTTON.SAVE')}
                    buttonColor={colors.primary}
                    onPress={() => handleSaveDocument()}
                    disable={canCTADisable()}
                    labelColors="#FFFF"
                    labelvariant="heading5"
                    buttonStyle={{
                      flex: 1,
                      borderRadius: 4,
                      paddingHorizontal: 83.5,
                      paddingVertical: 10,
                    }}
                    labelStyle={{
                      textTransform: 'uppercase',
                      fontWeight: '700',
                    }}
                  />
                  <Button
                    label={t('BUTTON.CANCEL')}
                    buttonColor="#B6CFF3"
                    onPress={() => {
                      files.forEach(async (file) => {
                        if (file.objectKey) {
                          await deleteFileFromS3({
                            objectKey: file.objectKey,
                            BusinessUnitFilter__c:
                              opportunityDetails.BusinessUnitFilter__c,
                          })
                        }
                      })
                      handleClose()
                      resetModal()
                    }}
                    appearance="outline"
                    labelColors={colors.primary}
                    buttonStyle={{
                      flex: 1,
                      borderRadius: 4,
                      paddingHorizontal: 83.5,
                      paddingVertical: 10,
                    }}
                    labelStyle={{ textTransform: 'uppercase' }}
                  />
                </View>
              )}
            </View>
          </View>
        </View>
      </BlurView>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000CC',
    width: '100%',
    paddingHorizontal: window.innerWidth <= 440 ? 24 : 40,
    justifyContent: 'center',
  },
  card: {
    alignSelf: 'center',
    backgroundColor: '#ffffff',
    paddingHorizontal: window.innerWidth <= 440 ? 20 : 40,
    paddingVertical: window.innerWidth <= 440 ? 16 : 35,
    maxWidth: 502,
    width: '100%',
    borderRadius: 15,
    display: 'flex',
    flexDirection: 'column',
  },
  header: {
    position: 'relative',
    flexShrink: 0, // Prevent header from shrinking
  },
  scrollContent: {
    flex: 1,
    marginVertical: 10,
    maxHeight: 150, // Maximum height constraint
  },
  scrollContentContainer: {
    flexGrow: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexShrink: 0, // Prevent button container from shrinking
    marginTop: 10,
    justifyContent: 'center',
  },
  overlay: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(11, 92, 215, 0.2)',
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginTop: 6,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0B5CD7',
    borderRadius: 2,
    transition: 'width 0.3s ease',
  },
  // Screenshot-based Design Styles
  uploadContainer: {
    alignItems: 'start',
    paddingBottom: 20,
    paddingTop: 8,
  },
  uploadButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    padding: 3,
  },
  divider: {
    borderBottomWidth: 1,
    borderColor: '#162447',
    opacity: '10%',
    marginTop: 12,
  },
  uploadHint: {
    fontSize: 12,
    textAlign: 'center',
  },
  fileListContainer: {
    marginTop: 16,
    marginBottom: 20,
  },
  // New document card design matching screenshot
  documentCard: {
    backgroundColor: '#F3F3F3',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
    width: 410, // Fixed width for horizontal scrolling
    minWidth: 410,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileIconContainer: {
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentIcon: {
    height: 40,
    width: 32,
  },
  fileBasicInfo: {
    flex: 1,
    marginRight: 12,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  documentType: {
    fontSize: 14,
    color: '#666666',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusIconContainer: {
    marginRight: 4,
  },
  cardFooter: {
    minHeight: 'auto',
  },
  uploadingContainer: {
    marginTop: 4,
  },
  errorContainer: {
    marginTop: 4,
    padding: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4444',
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  retryButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: 'rgba(11, 92, 215, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(11, 92, 215, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButton: {
    borderRadius: 6,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  retryHintText: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  successText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  uploadStatusText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  uploadAnotherButton: {
    alignItems: 'center',
    paddingTop: 20,
  },
  uploadAnotherText: {
    fontSize: 14,
    fontWeight: '700',
  },
  scrollView: {
    marginBottom: 12,
  },
  scrollContainer: {
    paddingHorizontal: 4,
    gap: 12,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  navButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  countIndicator: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Enhanced carousel styles
  documentCardActive: {
    borderWidth: 1,
    borderColor: '#0B5CD7',
    shadowColor: '#0B5CD7',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  documentCardRemoving: {
    opacity: 0.6,
    backgroundColor: '#F0F0F0',
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  positionIndicatorContainer: {
    alignItems: 'center',
    gap: 8,
  },
  positionIndicator: {
    fontSize: 12,
    fontWeight: '600',
  },
  dotContainer: {
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#E0E0E0',
  },
  dotActive: {
    backgroundColor: '#0B5CD7',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  fileDocumentTypeContainer: {
    marginTop: 8,
    marginBottom: 4,
  },
  fileDocumentTypeDropdown: {
    margin: 0,
    minHeight: 32,
  },
})

export default MultiFileUploadModal
