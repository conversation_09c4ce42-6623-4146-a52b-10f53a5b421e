import React, { useState } from 'react'
import {
  View,
  StyleSheet,
  Image,
  ImageBackground,
  Pressable,
} from 'react-native'
import { Text } from '@libs/components'
import { useTranslation } from 'react-i18next'
import { useTheme } from '@libs/theme'
import { Icon } from '@app-hero/native-icons'
import { PlayButtonCMS } from '@apphero/assets'

const NeedSupportAccordion = ({
  children,
  imageSrc,
  style,
  onShowVideo = () => {},
  canHaveVideo,
  supportSections,
  handleNext,
  handlePrev,
  currentIndex,
}) => {
  const { colors } = useTheme()
  const { t } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <View style={[styles.rightContainer, style]}>
      <View style={{ paddingHorizontal: 20, paddingVertical: 24 }}>
        <Pressable
          onPress={() => setIsOpen(!isOpen)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            columnGap: 16,
          }}
        >
          <Icon
            name={isOpen ? 'ArrowUp' : 'ArrowDown'}
            width={24}
            height={24}
            style={{ fontWeight: 700 }}
          />
          <Text
            variant="display1"
            style={{
              color: colors.textPrimary,
              fontSize: 20,
              fontWeight: 700,
            }}
          >
            {t('APPLICATION_DETAIL.ANY_QUESTION')}
          </Text>
        </Pressable>

        {isOpen && (
          <>
            <View style={{ flexDirection: 'column' }}>
              <View style={styles.divider} />

              {children}

              {imageSrc && (
                <View style={styles.imageSliderContainer}>
                  <Pressable
                    style={styles.imageWrapper}
                    onPress={onShowVideo}
                    disabled={!canHaveVideo}
                  >
                    <ImageBackground
                      source={supportSections[currentIndex].image}
                      style={{
                        width: '100%',
                        aspectRatio: 16 / 9,
                        borderRadius: 4,
                        flex: 1,
                      }}
                      resizeMode="cover"
                    >
                      <View
                        style={{
                          flex: 1,
                          alignItems: 'center',
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          paddingHorizontal: 16,
                        }}
                      >
                        <Pressable
                          onPress={handlePrev}
                          disabled={currentIndex === 0}
                          style={[
                            styles.arrow,
                            styles.arrowLeft,
                            currentIndex === 0 && styles.disabledArrow,
                          ]}
                        >
                          <Icon
                            name="ArrowUp"
                            width={24}
                            height={24}
                            style={styles.rotateLeft}
                          />
                        </Pressable>

                        <Image
                          source={PlayButtonCMS}
                          style={{ width: 45, height: 45 }}
                          resizeMode="contain"
                        />

                        <Pressable
                          onPress={handleNext}
                          disabled={currentIndex === supportSections.length - 1}
                          style={[
                            styles.arrow,
                            styles.arrowRight,
                            currentIndex === supportSections.length - 1 &&
                              styles.disabledArrow,
                          ]}
                        >
                          <Icon
                            name="ArrowUp"
                            width={24}
                            height={24}
                            style={styles.rotateRight}
                          />
                        </Pressable>
                      </View>
                    </ImageBackground>
                  </Pressable>
                </View>
              )}
            </View>

            <View style={styles.indicatorContainer}>
              {supportSections &&
                Array.isArray(supportSections) &&
                Array.from({ length: supportSections.length || 0 }).map(
                  (_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.indicator,
                        index === currentIndex
                          ? styles.activeIndicator
                          : styles.inactiveIndicator,
                      ]}
                    />
                  ),
                )}
            </View>
          </>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  rightContainer: {
    borderRadius: 13,
    background: 'transparent',
    borderWidth: 0.2,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    backgroundColor: '#DDE0EE',
    width: '100%',
    flexDirection: 'column',
  },
  divider: {
    borderBottomWidth: 2,
    borderColor: 'rgba(0,0,0,0.2)',
    borderStyle: 'solid',
    opacity: 0.2,
    marginVertical: 20,
  },
  imageSliderContainer: {
    position: 'relative',
    width: '100%',
    alignSelf: 'center',
    marginTop: 28,
    paddingHorizontal: 14,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
  },
  imageWrapper: {
    width: '100%',
    aspectRatio: 16 / 9,
    borderRadius: 4,
    overflow: 'hidden',
  },
  arrow: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: '50%',
  },
  arrowLeft: {
    left: -8,
    transform: [{ translateY: 0 }],
  },
  arrowRight: {
    right: -8,
    transform: [{ translateY: 0 }],
  },
  rotateLeft: {
    transform: [{ rotate: '-90deg' }],
  },
  rotateRight: {
    transform: [{ rotate: '90deg', translateX: -1 }],
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 6,
    marginTop: 20,
  },
  indicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  activeIndicator: {
    width: 40, // Makes the active one longer
    backgroundColor: '#316AFF', // Blue color
    borderRadius: 10, // Keeps it rounded
  },
  inactiveIndicator: {
    backgroundColor: '#A0A0A0', // Gray color
  },
  disabledArrow: {
    opacity: 0.2, // Faded when disabled
  },
})

export default NeedSupportAccordion
