import React from 'react'
import { View, StyleSheet, Image, Pressable } from 'react-native'
import { Text } from '@libs/components'
import { useTranslation } from 'react-i18next'
import { useTheme } from '@libs/theme'
import { PlayButtonCMS } from '@apphero/assets'
import { Icon } from '@app-hero/native-icons'

const NeedSupport = ({
  children,
  imageSrc,
  imageAlt,
  style,
  onShowVideo = () => {},
  canHaveVideo,
  supportSections,
  handleNext,
  handlePrev,
  currentIndex,
}) => {
  const { colors } = useTheme()
  const { t } = useTranslation()

  return (
    <View style={[styles.rightContainer, style]}>
      <View style={{ paddingHorizontal: 32, paddingVertical: 24 }}>
        <Text
          variant="display1"
          style={{
            color: colors.textPrimary,
            fontSize: 20,
            fontWeight: 700,
          }}
        >
          {t('APPLICATION_DETAIL.ANY_QUESTION')}
        </Text>
        <View style={styles.divider} />

        <Text
          variant="display4"
          color={colors.neutral}
          style={{ width: 350, lineHeight: 21 }}
        >
          {children}
        </Text>

        <Pressable
          style={{
            padding: 14,
            backgroundColor: colors.white,
            marginTop: 28,
            borderRadius: 8,
          }}
          onPress={onShowVideo}
          disabled={!canHaveVideo}
        >
          {supportSections?.[currentIndex]?.image && (
            <Image
              source={supportSections?.[currentIndex]?.image}
              style={{
                height: 170,
                width: '100%',
                borderRadius: 4,
              }}
              alt={imageAlt}
            />
          )}
          {canHaveVideo ? (
            <View
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: [{ translateX: -30 }, { translateY: -18 }],
              }}
            >
              <Image
                source={PlayButtonCMS}
                style={{ width: 45, height: 45 }}
                resizeMode="contain"
              />
            </View>
          ) : null}
        </Pressable>

        <View style={styles.arrows}>
          <Pressable
            onPress={handlePrev}
            // style={styles.arrowButton}
            disabled={currentIndex === 0}
            style={[
              styles.arrowButton,
              currentIndex === 0 && styles.disabledArrow,
            ]}
          >
            <Icon
              name="ArrowUp"
              width={24}
              height={24}
              // color={currentIndex===0 ? '#000000' : '#DDDFEE'}
              style={{ transform: [{ rotate: '-90deg' }] }}
            />
          </Pressable>

          <View style={styles.indicatorContainer}>
            {supportSections &&
              Array.isArray(supportSections) &&
              Array.from({ length: supportSections.length || 0 }).map(
                (_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.indicator,
                      index === currentIndex
                        ? styles.activeIndicator
                        : styles.inactiveIndicator,
                    ]}
                  />
                ),
              )}
          </View>

          <Pressable
            onPress={handleNext}
            disabled={currentIndex === supportSections.length - 1}
            style={[
              styles.arrowButton,
              currentIndex === supportSections.length - 1 &&
                styles.disabledArrow,
            ]}
          >
            <Icon
              name="ArrowUp"
              width={24}
              height={24}
              // color={currentIndex===0 ? '#000000' : '#DDDFEE'}
              style={{ transform: [{ rotate: '90deg' }] }}
            />
          </Pressable>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  rightContainer: {
    borderRadius: 13,
    background: 'transparent',
    borderWidth: 0.2,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    backgroundColor: '#DDE0EE',
    minWidth: window.innerWidth >= 1260 ? 400 : 300,
    alignSelf: 'baseline',
  },
  divider: {
    borderBottomWidth: 2,
    borderColor: 'rgba(0,0,0,0.2)',
    borderStyle: 'solid',
    opacity: 0.2,
    marginVertical: 20,
  },
  arrows: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    gap: 12, // Adds space between arrows
    color: 'rgba(0, 0, 0, 0.83)',
  },
  arrowButton: {
    height: '1.8rem',
    width: '1.8rem',
  },
  indicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  indicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  activeIndicator: {
    width: 40, // Makes the active one longer
    backgroundColor: '#316AFF', // Blue color
    borderRadius: 10, // Keeps it rounded
  },
  inactiveIndicator: {
    backgroundColor: '#A0A0A0', // Gray color
  },
  disabledArrow: {
    opacity: 0.2, // Faded when disabled
  },
})

export default NeedSupport
