import React, { useEffect } from 'react'
import {
  View,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  ScrollView,
} from 'react-native'
import { useTranslation } from 'react-i18next'
import {
  Text,
  TextInput,
  Button,
  DropDown,
  MobileInput,
} from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { useTheme } from '@libs/theme'
import { isMobile, isWeb } from '@libs/utils/src/screenLayout'
// useNavigation is provided by the hook
import { Image } from 'react-native-web'
import { HelpAndSupportConfirmation } from '@apphero/assets'
import {
  MetadataField,
  useHelpAndSupport,
} from '../../screens/helpAndSupport/helpAndSupportUtils'

const HelpAndSupport = ({ previousRouteName }) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const { width } = useWindowDimensions()

  const mobile = isMobile(width)
  const isDesktop = isWeb(width)

  // Create a minimal user profile for unauthenticated users
  const guestUserProfile = {
    firstName: '',
    lastName: '',
    email: '',
    mobileNumber: '',
    countryCode: '+375',
  }

  // Use the centralized hook for Help and Support functionality
  const {
    formData,
    errors,
    isValid,
    isSubmitting,
    isSubmitted,
    subjectDropdownRef,
    applicationDropdownRef,
    dropdownState,
    supportedBrands,
    subjectCategories,
    toggleSubjectDropdown,
    toggleApplicationDropdown,
    handleChange,
    handleSubmit,
    navigation,
  } = useHelpAndSupport(guestUserProfile)

  // Add metadata style for textarea placeholder
  useEffect(() => {
    if (!document.getElementById('stylemeta')) {
      const style = document.createElement('style')
      style.id = 'stylemeta'
      style.innerHTML = `
          #stylemeta::placeholder {
            color: #94A3B8;
          }
        `
      document.head.appendChild(style)
    }
  }, [])

  const handleBack = (routeName) => {
    navigation.navigate(routeName)
  }

  const getRouteName = (route) => {
    if (route === 'Sign in') {
      return 'login'
    }
    if (route === 'Signup') {
      return 'sign-up'
    }
    if (route === 'OTP') {
      return 'otp'
    }
    if (route === 'Set Password') {
      return '/set-password'
    }
    if (route === 'Forgot Password') {
      return '/forgot-password'
    }
    if (route === 'Set New Password') {
      return '/set-new-password'
    }
    if (route === 'Privacy policy') {
      return 'privacy-policy'
    }
    if (route === 'Help and Support') {
      return '/auth-help-support'
    }
    return 'Login'
  }

  return (
    <ScrollView style={styles.container(mobile)}>
      <View style={styles.content(mobile, isDesktop)}>
        {isSubmitted ? (
          <View style={styles.thankYouContainer}>
            <Image
              source={HelpAndSupportConfirmation}
              style={{
                height: 50,
                width: 50,
              }}
            />
            <Text variant="display1" style={styles.thankYouTitle}>
              {t('HELP_SUPPORT.THANK_YOU')}
            </Text>
            <Text variant="display4" style={styles.thankYouMessage}>
              {t('HELP_SUPPORT.SUBMISSION_RECEIVED')}
            </Text>
            <Button
              label={`Take me back to ${previousRouteName}`}
              buttonColor={colors.primary}
              labelColors={colors.white}
              onPress={() => handleBack(getRouteName(previousRouteName))}
              buttonStyle={styles.backToLoginButton}
              labelStyle={{ fontWeight: 700, fontSize: 16 }}
            />
          </View>
        ) : (
          <View style={styles.formContainer}>
            <View style={styles.formHeader}>
              {!isDesktop ? (
                <View
                  style={{
                    flexDirection: 'row',
                    flex: 1,
                    columnGap: 12,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      handleBack(getRouteName(previousRouteName))
                    }}
                  >
                    <Icon name="ArrowNarrowLeft" color={colors.neutral} />
                  </TouchableOpacity>
                  <View>
                    <Text
                      variant="display1"
                      style={[
                        styles.formTitle,
                        {
                          marginBottom: 4,
                          fontSize: 20,
                        },
                      ]}
                    >
                      {t('HELP_SUPPORT.QUOTES')}
                    </Text>
                  </View>
                </View>
              ) : (
                <Text
                  variant="display1"
                  style={[styles.formTitle, { marginBottom: 4 }]}
                >
                  {t('HELP_SUPPORT.QUOTES')}
                </Text>
              )}

              <Text variant="display4" style={styles.formSubtitle}>
                {t('HELP_SUPPORT.FILL_FORM')}
              </Text>
            </View>

            <View style={styles.formFields}>
              {/* Full Name */}
              <TextInput
                value={formData.fullName}
                onChangeText={(text) => handleChange('fullName', text)}
                placeholder="Full Name"
                limit={70}
                placeholderColor="#94A3B8"
                style={[styles.inputField]}
                iconName="User2"
                iconSize={20}
                inputFieldStyle={{ marginLeft: 12 }}
              />

              {/* Email */}
              <TextInput
                value={formData.email}
                onChangeText={(text) => handleChange('email', text)}
                placeholder="Email"
                style={[
                  styles.inputField,
                  errors.email && formData.email && { borderColor: 'red' },
                ]}
                placeholderColor="#94A3B8"
                iconName="Mail2"
                iconSize={20}
                inputFieldStyle={{ marginLeft: 12 }}
              />
              {errors.email && formData.email && (
                <Text style={[styles.errorText, { marginTop: 0 }]}>
                  {errors.email}
                </Text>
              )}

              {/* Phone Number */}
              <MobileInput
                value={formData.phoneNumber}
                onChangeText={(text) => handleChange('phoneNumber', text)}
                placeholder="Mobile number"
                placeholderColor="#94A3B8"
                style={[
                  styles.mobileInput,
                  errors.phoneNumber &&
                    formData.phoneNumber && { borderColor: 'red' },
                ]}
                handleCountrySelection={(selectedCountry) => {
                  handleChange('countryCode', selectedCountry.dial_code)
                }}
                code={formData.countryCode}
              />
              {errors.phoneNumber && formData.phoneNumber && (
                <Text style={[styles.errorText, { marginBottom: 4 }]}>
                  {errors.phoneNumber}
                </Text>
              )}

              {/* Subject/Category */}

              <View ref={subjectDropdownRef} style={{ marginTop: 8 }}>
                <DropDown
                  items={subjectCategories}
                  toggleDropdown={toggleSubjectDropdown}
                  position={{
                    left: dropdownState.subjectLeft,
                    top: dropdownState.subjectTop + 60,
                  }}
                  dropdownWidth={dropdownState.dropdownWidth}
                  onPress={(selectedOption) => {
                    handleChange('subject', selectedOption)
                  }}
                  borderColor="rgba(22, 36, 71, 0.2)"
                  haveUnderLine
                  label="Select Subject/ Category"
                  dropdownHeight={80}
                  style={[
                    styles.dropdownStyle,
                    errors.subject && { borderColor: 'red' },
                  ]}
                  labelStyle={{ padding: 100 }}
                  placeHolderColor="#94A3B8"
                  value={formData.subject}
                />
              </View>

              <View ref={applicationDropdownRef} style={{ marginTop: 12 }}>
                <DropDown
                  items={supportedBrands}
                  toggleDropdown={toggleApplicationDropdown}
                  position={{
                    left: dropdownState.applicationLeft,
                    top: dropdownState.applicationTop + 70,
                  }}
                  dropdownWidth={dropdownState.dropdownWidth}
                  onPress={(selectedOption) => {
                    handleChange('brand', selectedOption)
                  }}
                  haveUnderLine
                  label="Select Institution"
                  dropdownHeight={150}
                  style={{
                    ...styles.dropdownStyle,
                    ...{ marginVertical: 5 },
                    ...(errors.brand && { borderColor: 'red' }),
                  }}
                  labelStyle={{ padding: 100 }}
                  placeHolderColor="rgba(148, 163, 184, 1)"
                  value={formData.brand}
                  borderColor="rgba(22, 36, 71, 0.2)"
                />
              </View>

              {/* Message */}
              <View style={styles.messageContainer}>
                <MetadataField
                  onChange={(text) => handleChange('message', text)}
                  placeholder={t('HELP_SUPPORT.ENTER_MESSAGE')}
                  multiline
                  style={{
                    ...styles.messageInput,
                    ...{
                      border: '1px solid',
                      paddingHorizontal: 15,
                      borderColor: 'rgba(22, 36, 71, 0.2)',
                    },
                  }}
                  placeHolderColor="#94A3B8"
                  value={formData.message}
                  colors={colors}
                />
              </View>

              {errors.unauth && (
                <Text style={styles.errorText}>{errors.unauth}</Text>
              )}

              {/* Submit Button */}

              <View style={styles.buttonContainer(width)}>
                <TouchableOpacity
                  style={styles.submitButton(colors, !isValid || isSubmitting)}
                  onPress={handleSubmit}
                  disabled={!isValid || isSubmitting}
                >
                  <Text variant="display3" style={styles.submitButtonText}>
                    {isSubmitting ? 'Sending...' : 'Send Message!'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: (mobile) => ({
    flex: 1,
    paddingRight: mobile ? 16 : 32,
    paddingLeft: mobile ? 16 : 0,
    paddingTop: 24,
  }),
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 40,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backText: {
    marginLeft: 8,
    fontWeight: '600',
    color: '#000',
  },
  content: (mobile, isDesktop) => ({
    width: '100%',
    maxWidth: isDesktop ? 800 : '100%',
    alignSelf: 'center',
    borderRadius: 12,
    padding: mobile ? 16 : 24,
  }),
  formContainer: {
    width: '100%',
  },
  formHeader: {
    marginBottom: 8,
    paddingBottom: 16,
    gap: 4,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 700,
    marginBottom: 12,
    color: '#0F172A',
  },
  formSubtitle: {
    color: '#64748B',
    marginBottom: 4,
  },
  formFields: {
    width: '100%',
  },
  inputWrapper: {
    position: 'relative',
    marginBottom: 16,
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    top: 16,
    zIndex: 1,
  },
  inputField: {
    height: 56,
    borderRadius: 8,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    borderWidth: 1,
    fontSize: 16,
  },
  mobileInput: {
    height: 56,
    borderRadius: 8,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    padding: 0,
    marginBottom: 4,
  },
  dropdownStyle: {
    height: 56,
    borderRadius: 12,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    margin: 0,
  },
  messageContainer: {
    marginTop: 12,
    paddingRight: 18,
  },
  messageInput: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    borderColor: 'rgba(22, 36, 71, 0.2)',
    borderWidth: 1,
    paddingHorizontal: 20,
    paddingLeft: 14,
    paddingTop: 14,
    marginBottom: 12,
    fontSize: 14,
    fontFamily: 'Inter, sans-serif',
    resize: 'none',
    outline: 'none',
    textAlignVertical: 'top',
  },
  buttonContainer: () => ({
    width: '100%',
    paddingVertical: 12,
  }),
  submitButton: (colors, canDisable) => ({
    backgroundColor: colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 50,
    borderRadius: 8,
    opacity: canDisable ? 0.5 : 1,
  }),
  submitButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
    textAlign: 'center',
  },
  thankYouContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    height: '60vh',
    width: '100%',
  },

  thankYouTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 24,
    marginBottom: 12,
  },
  thankYouMessage: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    maxWidth: 400,
  },
  backToLoginButton: {
    width: 300,
    marginTop: 16,
    borderRadius: 4,
    height: 56,
  },
  errorText: {
    color: 'red',
    fontSize: 14,
    marginTop: 6,
    marginBottom: 12,
  },
})

export default HelpAndSupport
