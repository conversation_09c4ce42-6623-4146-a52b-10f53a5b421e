import {
  View,
  StyleSheet,
  Platform,
  useWindowDimensions,
  TouchableOpacity,
  Modal,
  Image,
} from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import {
  Button,
  Link,
  LinkedInButton,
  Loader,
  PasswordInput,
  Text,
  TextInput,
} from '@libs/components'
import { BlurView } from 'expo-blur'
import { v4 as uuidv4 } from 'uuid'
import { Icon } from '@app-hero/native-icons'
import { useIsFocused, useNavigation, useRoute } from '@react-navigation/native'
import { useAtom } from 'jotai'
import { useTranslation } from 'react-i18next'
import { spacing, useTheme } from '@libs/theme'
import { CustomAuth, SecureStore } from '@libs/utils'
import { Auth } from 'aws-amplify'
import queryString from 'query-string'
import { SecurityShield } from '@apphero/assets'
import { isMobile, isTablet, isWeb } from '@libs/utils/src/screenLayout'
import {
  ResetPassword<PERSON>tom,
  errorMessageAtom,
  isChatbotVisible,
  linkedInAtom,
  userLanguage,
  userProfile,
} from '../../utils/atom'
import {
  createCloudWatchLog,
  getUserDetails,
  sendLoginFailureNotification,
} from '../../api'
import { reset } from '../../navigation/RootNavigator'
import ModalComponent from '../modal'

const Login = ({ isLoading, setIsLoading, errorMessage, setErrorMessage }) => {
  const [, setCanShowChatbot] = useAtom(isChatbotVisible)
  const [isCTAEnable, setIsCTAEnable] = useState(false)
  const navigation = useNavigation()
  const [, setUserDetails] = useAtom(userProfile)
  const [alertBoxDetails, setAlertBoxDetails] = useAtom(linkedInAtom)
  const [loginDetails, setLoginDetails] = useState({
    email: '',
    password: '',
  })
  const isFocused = useIsFocused()
  const [emailErrorMessage, setEmailErrorMessage] = useState({
    error: '',
    errorKey: '',
  })
  // Using state for UI, but will sync with localStorage
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false)

  // Helper functions for safely accessing localStorage
  const getSessionStorageItem = (key, defaultValue) => {
    try {
      return sessionStorage.getItem(key) || defaultValue
    } catch (error) {
      return defaultValue
    }
  }

  const setSessionStorageItem = (key, value) => {
    try {
      sessionStorage.setItem(key, value)
    } catch (error) {
      // Silently fail if localStorage is not available
    }
  }

  // Initialize from localStorage or default values
  useEffect(() => {
    if (isFocused) {
      // Check if we need to show the modal based on localStorage values
      const storedAttempts = getSessionStorageItem('failedLoginAttempts', '0')

      if (
        parseInt(storedAttempts, 10) === 2 &&
        errorMessage === 'Incorrect username or password.'
      ) {
        setShowForgotPasswordModal(true)
      }
    }
  }, [isFocused])
  const { colors } = useTheme()
  const { t } = useTranslation()

  const route = useRoute()

  const [userSelectedLanguage] = useAtom(userLanguage)
  const [, setForgotPasswordCredential] = useAtom(ResetPasswordAtom)
  const [, setError] = useAtom(errorMessageAtom)
  const [isInputFieldFocused, setIsInputFieldFocused] = useState({
    email: false,
    password: false,
  })

  const [isThrottled, setIsThrottled] = useState(false) // State to handle the throttling
  const timeoutRef = useRef(null)
  const lastAttemptRef = useRef(Date.now())

  const { width } = useWindowDimensions()
  const mobile = isMobile(width)
  const tablet = isTablet(width)
  const isDesktop = isWeb(width)

  const emailValidationPattern = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  const validateEmail = (email) => emailValidationPattern.test(email)

  useEffect(() => {
    if (!isFocused) return
    ;(() => {
      setEmailErrorMessage((prev) => ({ ...prev, error: t(prev.errorKey) }))
    })()
  }, [userSelectedLanguage, isFocused])

  useEffect(() => {
    if (!isFocused) return

    const handleKeyPress = (event) => {
      const now = Date.now()
      const THROTTLE_INTERVAL = 2000
      const MIN_PRESS_DURATION = 300

      // Validation checks
      if (event.code === 'Enter' || event.charCode === 13) {
        // Debounce (ignores extremely rapid presses)
        if (now - lastAttemptRef.current < MIN_PRESS_DURATION) return
        lastAttemptRef.current = now

        if (
          loginDetails?.email &&
          validateEmail(loginDetails?.email) &&
          loginDetails.password?.length >= 6 &&
          !isThrottled
        ) {
          setIsThrottled(true)
          handleSignIn()
          timeoutRef.current = setTimeout(() => {
            setIsThrottled(false)
          }, THROTTLE_INTERVAL)
        }
      }
    }

    if (Platform.OS === 'web') {
      window.addEventListener(
        'keypress',
        route.name === 'login' ? handleKeyPress : () => {},
      )
    }

    return () => {
      // Cleanup
      if (Platform.OS === 'web') {
        window.removeEventListener('keypress', handleKeyPress)
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [isFocused, loginDetails, isThrottled, isLoading])

  useEffect(() => {
    ;(async () => {
      try {
        await Auth.currentAuthenticatedUser()
      } catch (error) {
        setCanShowChatbot(false)
      }
    })()
  }, [isFocused])

  useEffect(() => {
    if (!isFocused) return
    ;(async () => {
      const emailPattern = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      const isValidEmail = emailPattern.test(loginDetails?.email)
      if (isValidEmail && loginDetails?.password?.length >= 6) {
        setIsCTAEnable(true)
      } else {
        setIsCTAEnable(false)
      }
      if (!isValidEmail && loginDetails?.email?.length > 0) {
        const disallowedCharsPattern = /[!#$%&'*+\-/=?^`{|}~,\s[\]<>\\]/
        if (disallowedCharsPattern.test(loginDetails?.email)) {
          setEmailErrorMessage({
            error: t('ERROR_MESSAGE.INVALID_EMAIL_2'),
            errorKey: 'ERROR_MESSAGE.INVALID_EMAIL_2',
          })
        } else {
          setEmailErrorMessage({
            error: t('ERROR_MESSAGE.INVALID_EMAIL_1'),
            errorKey: 'ERROR_MESSAGE.INVALID_EMAIL_1',
          })
        }
        setIsCTAEnable(false)
      }
      if (isValidEmail || loginDetails?.email?.length === 0) {
        setEmailErrorMessage({
          error: '',
          errorKey: '',
        })
      }
    })()
  }, [isFocused, loginDetails])

  useEffect(() => {
    if (!isFocused) return
    ;(async () => {
      const tempUser = JSON.parse(await SecureStore.getItemAsync('tempUser'))

      setLoginDetails((previousData) => ({
        ...previousData,
        email: tempUser?.email,
      }))
    })()
    return () => {
      setForgotPasswordCredential({
        email: '',
        code: '',
        newPassword: '',
        confirmPassword: '',
      })
      setError('')
    }
  }, [isFocused])

  const handleChange = (field, text) => {
    setEmailErrorMessage({
      error: '',
      errorKey: '',
    })
    setErrorMessage('')
    setLoginDetails((previousData) => ({ ...previousData, [field]: text }))
  }

  const handleSignIn = async () => {
    setIsLoading(true)
    try {
      const userData = await CustomAuth.emailSignIn(
        loginDetails.email.toLowerCase(),
        loginDetails.password,
      )

      if (userData?.message) {
        setErrorMessage(userData.message)
        if (userData?.name === 'NotAuthorizedException') {
          const storedAttempts = getSessionStorageItem(
            'failedLoginAttempts',
            '0',
          )
          const newFailedAttempts = parseInt(storedAttempts, 10) + 1

          // Store updated attempts count
          setSessionStorageItem(
            'failedLoginAttempts',
            newFailedAttempts.toString(),
          )

          // After 2 failed attempts, show popup and send email notification
          const hasNotificationBeenSent =
            getSessionStorageItem('hasNotificationBeenSent', 'false') === 'true'

          if (newFailedAttempts === 2 && !hasNotificationBeenSent) {
            // Send email notification
            const messageId = uuidv4()
            const notificationPayload = {
              input: {
                email: loginDetails.email.toLowerCase(),
                event: 'INCORRECT_LOGIN',
                messageDetails: {
                  messageId,
                },
              },
            }

            await sendLoginFailureNotification(notificationPayload)
            setSessionStorageItem('hasNotificationBeenSent', 'true')

            // Show popup
            setShowForgotPasswordModal(true)
          }
        }
        if (userData?.name === 'UserNotConfirmedException') {
          const storedUserNotConfirmedAttempts = getSessionStorageItem(
            'userNotConfirmedAttempts',
            '0',
          )
          const newUserNotConfirmedAttempts =
            parseInt(storedUserNotConfirmedAttempts, 10) + 1

          // Store updated attempts count
          setSessionStorageItem(
            'userNotConfirmedAttempts',
            newUserNotConfirmedAttempts.toString(),
          )

          if (newUserNotConfirmedAttempts === 2) {
            // Send email notification
            const messageId = uuidv4()
            const notificationPayload = {
              input: {
                email: loginDetails.email.toLowerCase(),
                event: 'USER_NOT_CONFIRMED_LOGIN',
                messageDetails: {
                  messageId,
                },
              },
            }
            await sendLoginFailureNotification(notificationPayload)
          }
        }

        const cloudWatchLoggerPayload = {
          logStream: `apphero-frontend-service/${loginDetails.email.toLowerCase()}`,
          payload: { email: loginDetails.email.toLowerCase() },
          response: userData,
          isErrorLog: true,
          errorMessage: userData?.message,
          event: 'FAILED_LOGIN',
          useCase: 'LOGIN',
        }
        createCloudWatchLog(cloudWatchLoggerPayload)
      } else {
        // Reset failed login attempts on successful login
        setSessionStorageItem('failedLoginAttempts', '0')
        setSessionStorageItem('hasNotificationBeenSent', 'false')
        setSessionStorageItem('userNotConfirmedAttempts', '0')

        setUserDetails((prev) => ({ ...prev, ...userData }))
        if (userData?.accessToken) {
          const data = await getUserDetails({ email: userData.email })
          setUserDetails((prev) => ({ ...prev, ...data }))
          setCanShowChatbot(true)
          const redirectUrl = route.params?.redirect
          if (redirectUrl) {
            const queryParams = queryString.parseUrl(redirectUrl)
            navigation.navigate(queryParams.url.slice(1), queryParams.query)
            setIsLoading(false)
            return
          }
          if (data?.canApply) {
            navigation.navigate('path-finder')
          } else {
            navigation.navigate('dashboard')
          }
        }
      }
    } catch (error) {
      console.error('SignIn error:', error)
      setErrorMessage('An unexpected error occurred. Please try again.')
    }
    setIsLoading(false)
  }

  return (
    <View style={[styles.loginContainer(mobile, tablet, isDesktop)]}>
      <Text
        color={colors.neutral}
        variant={isDesktop ? 'heading2' : 'heading3'}
        style={{
          textAlign: 'center',
          fontWeight: 700,
          marginTop: 0,
          borderRadius: 12,
        }}
      >
        {t('AUTH.ACCOUNT_SIGNIN')}
      </Text>
      <Text
        color={colors.primaryPlaceHolder}
        variant="display4"
        style={{ marginTop: 12, textAlign: 'center' }}
      >
        {t('AUTH.WELCOME')}
      </Text>
      <View style={styles.loginFields}>
        <TextInput
          value={loginDetails.email}
          onChangeText={(text) => handleChange('email', text)}
          placeholder={t('TEXT_INPUT.EMAIL')}
          style={{
            height: 56,
            marginBottom: 16,
            borderRadius: isDesktop ? 4 : 8,
            borderColor: isInputFieldFocused.email ? colors.primary : '#E2E8F0',
          }}
          iconName="Mail2"
          iconSize={24}
          inputFieldStyle={{
            fontWeight: '600',
            color: '#0F172A',
            fontSize: 16,
            marginLeft: 12,
          }}
          onFocus={() =>
            setIsInputFieldFocused((prevData) => ({
              ...prevData,
              email: true,
            }))
          }
          onBlur={() =>
            setIsInputFieldFocused((prevData) => ({
              ...prevData,
              email: false,
            }))
          }
        />
        {emailErrorMessage.error ? (
          <Text
            variant="display3"
            color={colors.onAlert}
            style={{ marginBottom: 20 }}
          >
            {emailErrorMessage.error}
          </Text>
        ) : null}
        <PasswordInput
          value={loginDetails.password}
          onChangeText={(text) => handleChange('password', text)}
          placeholder={t('TEXT_INPUT.PASSWORD')}
          iconSize={24}
          style={{
            height: 56,
            marginBottom: 16,
            borderRadius: isDesktop ? 4 : 8,
            borderColor: isInputFieldFocused.password
              ? colors.primary
              : '#E2E8F0',
          }}
          iconName="Lock"
          inputFieldStyle={{
            fontWeight: '600',
            color: '#0F172A',
            fontSize: 16,
            marginLeft: 12,
          }}
          onFocus={() =>
            setIsInputFieldFocused((prevData) => ({
              ...prevData,
              password: true,
            }))
          }
          onBlur={() =>
            setIsInputFieldFocused((prevData) => ({
              ...prevData,
              password: false,
            }))
          }
        />
        <View style={styles.forgotLinkContainer}>
          <View />
          <TouchableOpacity
            onPress={() => navigation.navigate('forgot-password')}
          >
            <Text
              variant="display4"
              underline
              style={{
                marginTop: spacing.spacing3,
                color: colors.primary,
                fontWeight: 700,
              }}
            >
              {t('LINK.FORGOT_PASSWORD')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      {errorMessage && (
        <Text variant="display3" color={colors.onAlert}>
          {errorMessage === 'User is not confirmed.'
            ? 'Email not verified. Please sign up to complete verification.'
            : errorMessage}
        </Text>
      )}
      <View style={styles.buttonsContainer}>
        {isLoading ? (
          <Loader />
        ) : (
          <Button
            label={t('BUTTON.SIGNIN')}
            buttonColor={colors.primary}
            labelColors={colors.white}
            onPress={() => handleSignIn()}
            disable={!isCTAEnable}
            labelStyle={{ fontWeight: 700, fontSize: 16 }}
            buttonStyle={{ width: '100%', borderRadius: 4, height: 56 }}
          />
        )}
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <View
            style={{
              height: 1,
              width: '35%',
              borderStyle: 'solid',
              borderColor: '#E2E8F0',
              borderBottomWidth: 0.5,
            }}
          />
          <View>
            <Text
              variant="display5"
              textAlign="center"
              style={{ marginVertical: 24, marginHorizontal: 8 }}
            >
              {t('AUTH.OR')}
            </Text>
          </View>
          <View
            style={{
              height: 1,
              width: '35%',
              borderStyle: 'solid',
              borderColor: '#E2E8F0',
              borderBottomWidth: 0.5,
            }}
          />
        </View>

        <View style={[styles.authLinks, { width: '100%' }]}>
          <LinkedInButton
            onPress={() => CustomAuth.federatedSignIn('linkedin')}
          />
        </View>
      </View>
      <View style={styles.footer(mobile)}>
        <Text variant="display4">{t('LINK.DONT_HAVE_ACCOUNT')} </Text>
        <Link
          label={t('BUTTON.SIGNUP')}
          labelVariant="display4"
          labelColor={colors.primary}
          handlePress={() => navigation.navigate('sign-up')}
          labelStyle={{ fontWeight: 700 }}
        />
      </View>

      <ModalComponent
        visible={alertBoxDetails.visible}
        title={alertBoxDetails.message}
        buttonLabel={t('BUTTON.OK')}
        handleCloseModal={() => {
          if (alertBoxDetails.message !== t('AUTH.UNKNOWN_ERROR')) {
            reset('login')
          }
          setAlertBoxDetails({
            visible: false,
            message: '',
            code: '',
          })
        }}
        handleButton={() => {
          if (alertBoxDetails.message !== t('AUTH.UNKNOWN_ERROR')) {
            reset('login')
          }

          setAlertBoxDetails({
            visible: false,
            message: '',
            code: '',
          })
        }}
        buttonStyle={{
          borderRadius: 4,
        }}
        titleVariant="display3"
        labelStyle={{ fontWeight: 700 }}
        buttonColor="#2563EB"
        labelColors="white"
      />

      {/* Forgot Password Modal */}
      <Modal
        visible={showForgotPasswordModal}
        transparent
        onRequestClose={() => setShowForgotPasswordModal(false)}
      >
        <BlurView intensity={80} style={styles.overlay}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer(mobile)}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowForgotPasswordModal(false)}
              >
                <Icon
                  name="Close"
                  height={23}
                  width={23}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>

              <View style={styles.modalContent(mobile)}>
                <View style={styles.securityIcon(mobile)}>
                  <Image
                    source={SecurityShield}
                    style={styles.securityIcon(mobile)}
                    resizeMode="contain"
                  />
                </View>

                <View>
                  <Text style={styles.modalTitle(mobile)}>
                    Recover your account
                  </Text>

                  <Text style={styles.modalMessage(mobile)}>
                    {`${getSessionStorageItem(
                      'failedLoginAttempts',
                      '0',
                    )} Unsuccessful Login Attempts - `}
                    Reset Your AppHero Password
                  </Text>
                </View>

                <View style={styles.modalButtonsContainer(mobile)}>
                  <TouchableOpacity
                    style={styles.resetButton(mobile)}
                    onPress={() => {
                      setShowForgotPasswordModal(false)
                      setSessionStorageItem('failedLoginAttempts', '3')
                      navigation.navigate('forgot-password')
                    }}
                  >
                    <Text style={styles.resetButtonText(mobile)}>
                      Reset Password
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.tryAgainButton(mobile)}
                    onPress={() => setShowForgotPasswordModal(false)}
                  >
                    <Text style={styles.tryAgainButtonText(mobile)}>
                      No, I want to try again
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </BlurView>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  authLinks: {
    alignSelf: 'center',
  },
  buttonsContainer: {
    width: '100%',
    marginTop: 20,
    marginBottom: 30,
  },
  loginContainer: (mobile, tablet, isDesktop) => {
    return {
      flexDirection: 'column',
      alignItems: isDesktop && 'center',
      justifyContent: 'center',
      marginTop: !isDesktop ? '6vh' : '10vh',
      paddingLeft: isDesktop ? '10%' : '6%',
      paddingRight: isDesktop
        ? window.innerWidth <= 1140
          ? '0%'
          : '14%'
        : '6%',
    }
  },
  loginFields: {
    marginTop: 30,
    width: '100%',
  },
  footer: () => ({
    marginTop: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  }),
  forgotLinkContainer: {
    flex: 1,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(11, 92, 215, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: (mobile) => ({
    backgroundColor: 'white',
    borderRadius: 12,
    width: '90%',
    maxWidth: 502,
    height: mobile ? '90%' : 'auto',
    paddingHorizontal: mobile ? 20 : 32,
    paddingVertical: mobile ? 32 : 32,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  }),
  closeButton: {
    position: 'absolute',
    right: 15,
    top: 15,
    zIndex: 1,
  },
  closeButtonText: {
    fontSize: 24,
    color: '#666',
  },
  modalContent: (mobile) => ({
    alignItems: 'center',
    paddingTop: mobile ? 60 : 40,
    justifyContent: 'space-between',
    height: mobile ? '100%' : 'auto',
  }),
  securityIcon: (mobile) => ({
    width: mobile ? 150 : 120,
    height: mobile ? 150 : 120,
    marginBottom: mobile ? 24 : 20,
    marginTop: mobile ? 20 : 0,
  }),
  modalTitle: (mobile) => ({
    fontSize: mobile ? 22 : 20,
    fontWeight: 'bold',
    marginBottom: mobile ? 20 : 15,
    textAlign: 'center',
    color: '#333',
  }),
  modalMessage: (mobile) => ({
    fontSize: mobile ? 16 : 14,
    textAlign: 'center',
    marginBottom: mobile ? 40 : 32,
    color: '#666',
    lineHeight: mobile ? 24 : 20,
    paddingHorizontal: mobile ? 16 : 80,
  }),
  modalMessageHighlight: {
    color: '#333',
  },
  modalButtonsContainer: (mobile) => ({
    width: '100%',
    flexDirection: mobile ? 'column' : 'row',
    gap: mobile ? 12 : 16,
    justifyContent: 'space-between',
    marginTop: mobile ? 8 : 8,
    marginBottom: mobile ? 8 : 0,
  }),

  resetButton: (mobile) => ({
    backgroundColor: '#2563EB',
    paddingVertical: mobile ? 18 : 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  }),
  resetButtonText: (mobile) => ({
    color: 'white',
    fontWeight: 'bold',
    fontSize: mobile ? 18 : 16,
    textAlign: 'center',
  }),
  tryAgainButton: (mobile) => ({
    paddingVertical: mobile ? 18 : 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
    borderWidth: 1,
    borderColor: '#B6CFF3',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  }),
  tryAgainButtonText: (mobile) => ({
    color: '#2563EB',
    fontWeight: 'normal',
    fontSize: mobile ? 18 : 16,
    textAlign: 'center',
  }),
  overlay: {
    flex: 1,
  },
})

export default Login
