/* eslint-disable react/jsx-no-useless-fragment */

import React from 'react'
import { View, Text } from 'react-native'
import EmptyContainer from '../container/EmptyContainer'
import { useTheme } from '@libs/theme'

export const InformationSection = (props) => {
  const { data } = props

  if (data?.length === 0) {
    return <EmptyContainer hasFixedHight />
  }

  return (
    <>
      {data?.map((item, index) => (
        <SubMenu item={item} index={index} />
      ))}
    </>
  )
}

const SubMenu = ({ item, index }) => {
  const keyName = item?.displayName
  let value = item?.fieldName

  if (keyName === 'Intake') {
    value = item?.fieldName
  }

  const { colors } = useTheme()
  return (
    <View
      style={{
        flexDirection: 'row',
        paddingRight: 40,
        marginTop: 12,
        alignItems: 'center',
        justifyContent: 'flex-start',
      }}
      key={index}
    >
      <View style={{ marginVertical: 6, width: '40%' }}>
        <Text variant="display4" color={colors.text}>
          {keyName}:
        </Text>
      </View>
      <View style={{ width: '50%' }}>
        <Text
          style={{
            marginLeft: 30,
          }}
          variant="display4"
          color={colors.neutral}
          numberOfLines={3}
        >
          {value}
        </Text>
      </View>
    </View>
  )
}
