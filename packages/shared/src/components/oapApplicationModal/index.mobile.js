import {
  View,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Text } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { useTheme } from '@libs/theme'

const OapPortalMobile = ({
  portalUrl,
  redirectPath = '/application-filter',
  token,
  onClose = () => {},
}) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const iframeRef = useRef(null)
  const { width } = useWindowDimensions()
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')

  // This is already the mobile version, no need to check width

  useEffect(() => {
    // Function to send tokens to the iframe when it loads
    const sendTokensToIframe = async () => {
      try {
        setIsLoading(true)

        localStorage.setItem(
          'redirect-to-oap',
          JSON.stringify({
            enable: true,
            oapName: 'UCW',
          }),
        )

        // Get tokens from utility function

        if (!token) {
          window.location.href =
            'https://ucw-student-oap-dev.auth.eu-west-1.amazoncognito.com/oauth2/authorize?redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Flinkedin-loader&response_type=code&client_id=mi02thfgb5ql794ejc72c0ae8&identity_provider=dev-apphero-idp&scope=email%20openid%20phone%20profile%20aws.cognito.signin.user.admin'
        }

        const iframe = document.getElementById('ucw-portal-iframe')

        setIsLoading(false)

        if (iframe) {
          // Add a load event listener to the iframe
          const iframeLoadHandler = () => {
            setIsLoading(false)
            console.log('Mobile iframe loaded')

            try {
              // Create message with tokens and redirect instruction
              const message = {
                type: 'AUTH_REQUEST',
                code: token,
                redirectTo: redirectPath,
                timestamp: new Date().getTime(),
                isMobile: true,
              }

              console.log('Sending message to iframe:', message)

              // Send the message to the iframe
              const targetOrigin = new URL(portalUrl).origin
              console.log('Target origin:', targetOrigin)

              iframe.contentWindow.postMessage(message, targetOrigin)
              console.log('Message sent to iframe')

              // Add event listener to receive responses from the iframe
              const messageHandler = function (event) {
                // Verify the origin for security
                const targetOrigin = new URL(portalUrl).origin
                if (event.origin !== targetOrigin) {
                  // Ignore messages from other origins
                  return
                }

                // Only log if the data has a specific structure we're interested in
                if (event.data && typeof event.data === 'object') {
                  // Check if this is a response to our authentication message
                  if (
                    event.data.type === 'AUTH_RESPONSE' ||
                    event.data.status ||
                    event.data.success !== undefined ||
                    event.data.error
                  ) {
                    console.log(
                      'IMPORTANT - Auth response from iframe (mobile):',
                      event.data,
                    )
                  }
                  // Log navigation events
                  else if (
                    event.data.type === 'NAVIGATION' ||
                    event.data.redirected ||
                    event.data.path
                  ) {
                    console.log(
                      'IMPORTANT - Navigation event from iframe (mobile):',
                      event.data,
                    )
                  }
                  // Log any error messages
                  else if (event.data.type === 'ERROR') {
                    console.error(
                      'IMPORTANT - Error from iframe (mobile):',
                      event.data,
                    )
                  }
                }
              }

              // Remove any existing message listeners to avoid duplicates
              window.removeEventListener('message', messageHandler, false)

              // Add the message event listener
              window.addEventListener('message', messageHandler, false)
            } catch (error) {
              console.error('Error in postMessage:', error)
              setErrorMessage(
                'Error communicating with UCW Portal: ' + error.message,
              )
            }

            // Remove the event listener after it's been triggered
            iframe.removeEventListener('load', iframeLoadHandler)
          }

          // Add the load event listener
          iframe.addEventListener('load', iframeLoadHandler)
        }
      } catch (error) {
        console.error('Error sending tokens to iframe:', error)
        setErrorMessage(error.message)
        setIsLoading(false)
      }
    }

    sendTokensToIframe()
  }, [])

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} variant="heading3" color={colors.neutral}>
            {t('UCW_PORTAL.TITLE', 'UCW Portal')}
          </Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Icon name="Close" height={24} width={24} />
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading UCW Portal...</Text>
          </View>
        ) : errorMessage ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{errorMessage}</Text>
            <Button
              label={t('UCW_PORTAL.CLOSE', 'Close')}
              onPress={onClose}
              buttonStyle={styles.errorButton}
            />
          </View>
        ) : (
          <View style={styles.iframeContainer}>
            <iframe
              id="ucw-portal-iframe"
              ref={iframeRef}
              src={portalUrl}
              style={styles.iframe}
              title="UCW Portal"
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-downloads"
              allow="accelerometer; clipboard-write; encrypted-media; gyroscope; web-share"
            />
          </View>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    width: '100%',
    height: '100vh',
  },
  content: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    height: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  iframeContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginBottom: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorButton: {
    marginTop: 20,
    minWidth: 120,
  },
})

export default OapPortalMobile
