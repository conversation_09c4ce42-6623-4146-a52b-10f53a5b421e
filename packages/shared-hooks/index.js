import { useNotifications } from './src/client/useNotifications'
import { useDeleteBasketItem } from './src/mutation/useDeleteBasketItem'
import { useUpdateBasket } from './src/mutation/useUpdateBasket'
import { useUpdateCompareProgrammesId } from './src/mutation/useUpdateCompareprogrammesId'
import { useUpdateProfile } from './src/mutation/useUpdateProfile'
import { useUploadDocument } from './src/mutation/useUploadFile'
import { useBrandImage } from './src/query/useCoBrandImage'
import { useFilter } from './src/query/useFilter'
import { useInstitutionInfiniteData } from './src/query/useInstitution'
import {
  usePriceBookDetail,
  useProgramByID,
  useProgramsInfiniteData,
} from './src/query/usePrograms'
import { useUpcomingEvents } from './src/query/useUpcomingEvents'

export {
  useNotifications,
  useDeleteBasketItem,
  useUpdateBasket,
  useUpdateCompareProgrammesId,
  useUpdateProfile,
  useUploadDocument,
  useFilter,
  useInstitutionInfiniteData,
  usePriceBookDetail,
  useProgramByID,
  useProgramsInfiniteData,
  useBrandImage,
  useUpcomingEvents,
}
