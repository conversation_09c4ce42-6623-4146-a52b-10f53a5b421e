PODS:
  - boost (1.76.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.71.8)
  - FBReactNativeSpec (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.8)
    - RCTTypeSafety (= 0.71.8)
    - React-Core (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - Flipper (0.125.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.125.0):
    - FlipperKit/Core (= 0.125.0)
  - FlipperKit/Core (0.125.0):
    - Flipper (~> 0.125.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.125.0):
    - Flipper (~> 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.125.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.125.0)
  - FlipperKit/FKPortForwarding (0.125.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.125.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.71.8):
    - hermes-engine/Pre-built (= 0.71.8)
  - hermes-engine/Pre-built (0.71.8)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.1100)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.71.8)
  - RCTTypeSafety (0.71.8):
    - FBLazyVector (= 0.71.8)
    - RCTRequired (= 0.71.8)
    - React-Core (= 0.71.8)
  - React (0.71.8):
    - React-Core (= 0.71.8)
    - React-Core/DevSupport (= 0.71.8)
    - React-Core/RCTWebSocket (= 0.71.8)
    - React-RCTActionSheet (= 0.71.8)
    - React-RCTAnimation (= 0.71.8)
    - React-RCTBlob (= 0.71.8)
    - React-RCTImage (= 0.71.8)
    - React-RCTLinking (= 0.71.8)
    - React-RCTNetwork (= 0.71.8)
    - React-RCTSettings (= 0.71.8)
    - React-RCTText (= 0.71.8)
    - React-RCTVibration (= 0.71.8)
  - React-callinvoker (0.71.8)
  - React-Codegen (0.71.8):
    - FBReactNativeSpec
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/Default (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/DevSupport (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.8)
    - React-Core/RCTWebSocket (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-jsinspector (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-Core/RCTWebSocket (0.71.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-hermes
    - React-jsi (= 0.71.8)
    - React-jsiexecutor (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - Yoga
  - React-CoreModules (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/CoreModulesHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-RCTBlob
    - React-RCTImage (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-cxxreact (0.71.8):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-jsinspector (= 0.71.8)
    - React-logger (= 0.71.8)
    - React-perflogger (= 0.71.8)
    - React-runtimeexecutor (= 0.71.8)
  - React-hermes (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.71.8)
    - React-jsi
    - React-jsiexecutor (= 0.71.8)
    - React-jsinspector (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - React-jsi (0.71.8):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - React-jsinspector (0.71.8)
  - React-logger (0.71.8):
    - glog
  - react-native-netinfo (9.4.1):
    - React-Core
  - react-native-safe-area-context (4.7.1):
    - React-Core
  - React-perflogger (0.71.8)
  - React-RCTActionSheet (0.71.8):
    - React-Core/RCTActionSheetHeaders (= 0.71.8)
  - React-RCTAnimation (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTAnimationHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTAppDelegate (0.71.8):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.8):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTBlobHeaders (= 0.71.8)
    - React-Core/RCTWebSocket (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-RCTNetwork (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTImage (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTImageHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-RCTNetwork (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTLinking (0.71.8):
    - React-Codegen (= 0.71.8)
    - React-Core/RCTLinkingHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTNetwork (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTNetworkHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTSettings (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.8)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTSettingsHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-RCTText (0.71.8):
    - React-Core/RCTTextHeaders (= 0.71.8)
  - React-RCTVibration (0.71.8):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.8)
    - React-Core/RCTVibrationHeaders (= 0.71.8)
    - React-jsi (= 0.71.8)
    - ReactCommon/turbomodule/core (= 0.71.8)
  - React-runtimeexecutor (0.71.8):
    - React-jsi (= 0.71.8)
  - ReactCommon/turbomodule/bridging (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.8)
    - React-Core (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-logger (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - ReactCommon/turbomodule/core (0.71.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.8)
    - React-Core (= 0.71.8)
    - React-cxxreact (= 0.71.8)
    - React-jsi (= 0.71.8)
    - React-logger (= 0.71.8)
    - React-perflogger (= 0.71.8)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.19.1):
    - React-Core
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNLocalize (3.0.2):
    - React-Core
  - RNReanimated (3.0.1):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.23.0):
    - React-Core
    - React-RCTImage
  - RNSVG (12.5.1):
    - React-Core
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../../../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.125.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.125.0)
  - FlipperKit/Core (= 0.125.0)
  - FlipperKit/CppBridge (= 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.125.0)
  - FlipperKit/FBDefines (= 0.125.0)
  - FlipperKit/FKPortForwarding (= 0.125.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.125.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.125.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.125.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.125.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.125.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.125.0)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/DevSupport (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - "react-native-netinfo (from `../../../node_modules/@react-native-community/netinfo`)"
  - react-native-safe-area-context (from `../../../node_modules/react-native-safe-area-context`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../../../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../../../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCMaskedView (from `../../../node_modules/@react-native-masked-view/masked-view`)"
  - RNLocalize (from `../../../node_modules/react-native-localize`)
  - RNReanimated (from `../../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../../node_modules/react-native-screens`)
  - RNSVG (from `../../../node_modules/react-native-svg`)
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - libevent
    - OpenSSL-Universal
    - SocketRocket
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../../../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  react-native-netinfo:
    :path: "../../../node_modules/@react-native-community/netinfo"
  react-native-safe-area-context:
    :path: "../../../node_modules/react-native-safe-area-context"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../../../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../../../node_modules/@react-native-async-storage/async-storage"
  RNCMaskedView:
    :path: "../../../node_modules/@react-native-masked-view/masked-view"
  RNLocalize:
    :path: "../../../node_modules/react-native-localize"
  RNReanimated:
    :path: "../../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../../node_modules/react-native-screens"
  RNSVG:
    :path: "../../../node_modules/react-native-svg"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: f637f31eacba90d4fdeff3fa41608b8f361c173b
  FBReactNativeSpec: e9f5883d7aa975bbfa10151bce469090603688c1
  Flipper: 26fc4b7382499f1281eb8cb921e5c3ad6de91fe0
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cbdee19bdd4e7f05472a66ce290f1b729ba3cb86
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 47986d26692ae75ee7a17ab049caee8864f855de
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 8af6a32dfc2b65ec82193c2dee6e1011ff22ac2a
  RCTTypeSafety: bee9dd161c175896c680d47ef1d9eaacf2b587f4
  React: d850475db9ba8006a8b875d79e1e0d6ac8a0f8b6
  React-callinvoker: 6a0c75475ddc17c9ed54e4ff0478074a18fd7ab5
  React-Codegen: 786571642e87add634e7f4d299c85314ec6cc158
  React-Core: 1adfab153f59e4f56e09b97a153089f466d7b8aa
  React-CoreModules: 958d236715415d4ccdd5fa35c516cf0356637393
  React-cxxreact: 2e7a6283807ce8755c3d501735acd400bec3b5cd
  React-hermes: 8102c3112ba32207c3052619be8cfae14bf99d84
  React-jsi: dd29264f041a587e91f994e4be97e86c127742b2
  React-jsiexecutor: 747911ab5921641b4ed7e4900065896597142125
  React-jsinspector: c712f9e3bb9ba4122d6b82b4f906448b8a281580
  React-logger: 342f358b8decfbf8f272367f4eacf4b6154061be
  react-native-netinfo: fefd4e98d75cbdd6e85fc530f7111a8afdf2b0c5
  react-native-safe-area-context: 9697629f7b2cda43cf52169bb7e0767d330648c2
  React-perflogger: d21f182895de9d1b077f8a3cd00011095c8c9100
  React-RCTActionSheet: 0151f83ef92d2a7139bba7dfdbc8066632a6d47b
  React-RCTAnimation: 5ec9c0705bb2297549c120fe6473aa3e4a01e215
  React-RCTAppDelegate: 9895fd1b6d1176d88c4b10ddc169b2e1300c91f0
  React-RCTBlob: f3634eb45b6e7480037655e1ca93d1136ac984dd
  React-RCTImage: 3c12cb32dec49549ae62ed6cba4018db43841ffc
  React-RCTLinking: 310e930ee335ef25481b4a173d9edb64b77895f9
  React-RCTNetwork: b6837841fe88303b0c04c1e3c01992b30f1f5498
  React-RCTSettings: 600d91fe25fa7c16b0ff891304082440f2904b89
  React-RCTText: a0a19f749088280c6def5397ed6211b811e7eef3
  React-RCTVibration: 43ffd976a25f6057a7cf95ea3648ba4e00287f89
  React-runtimeexecutor: 7c51ae9d4b3e9608a2366e39ccaa606aa551b9ed
  ReactCommon: 85c98ab0a509e70bf5ee5d9715cf68dbf495b84c
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNCAsyncStorage: f47fe18526970a69c34b548883e1aeceb115e3e1
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNLocalize: dbea38dcb344bf80ff18a1757b1becf11f70cae4
  RNReanimated: 01715fef3a1153cb70c1efcff60b7b26580b9a8a
  RNScreens: 6a8a3c6b808aa48dca1780df7b73ea524f602c63
  RNSVG: d7d7bc8229af3842c9cfc3a723c815a52cdd1105
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 065f0b74dba4832d6e328238de46eb72c5de9556
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 6eaa7c2b0c94e48b9fc8e171d5a6e68a561d84a9

COCOAPODS: 1.12.1
