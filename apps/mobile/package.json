{"name": "mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android ", "android:bundle": "cd android && ./gradlew bundleRelease", "android:clean": "cd android && ./gradlew clean", "android:apk": "cd android && ./gradlew app:assembleRelease ", "ios": "react-native run-ios", "start": "react-native start", "studio": "studio android", "test": "jest", "xcode": "xed ios", "ios:build": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios", "lint": "eslint ."}, "dependencies": {"react": "18.2.0", "react-native": "0.71.8", "@react-native-async-storage/async-storage": "^1.19.1", "@react-native-community/netinfo": "^9.4.1", "@react-native-masked-view/masked-view": "^0.2.9", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@react-navigation/stack": "^6.3.17", "rn-fetch-blob": "^0.12.0", "react-native-safe-area-context": "^4.7.1", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-localize": "^3.0.2", "react-native-svg": "^12.1.1"}, "devDependencies": {"@babel/core": "7.22.6", "@babel/preset-env": "7.22.6", "@babel/runtime": "7.22.6", "@react-native-community/eslint-config": "3.2.0", "@tsconfig/react-native": "2.0.2", "@types/jest": "29.2.1", "@types/react": "18.0.24", "@types/react-test-renderer": "18.0.0", "babel-jest": "29.2.1", "eslint": "8.19.0", "jest": "29.2.1", "metro-react-native-babel-preset": "0.73.9", "prettier": "2.4.1", "react-test-renderer": "18.2.0", "react-native-reanimated": "3.0.1", "react-native-screens": "^3.23.0", "react-native-gesture-handler": "^2.12.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}