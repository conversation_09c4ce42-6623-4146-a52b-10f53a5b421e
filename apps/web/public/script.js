
	var initESW = function (gslbBaseURL) {
		embedded_svc.settings.displayHelpButton = true; //Or false
		embedded_svc.settings.language = ''; //For example, enter 'en' or 'en-US'


		embedded_svc.settings.prepopulatedPrechatFields = {
			FirstName: JSON.parse(localStorage.getItem('userProfile'))?.firstName||"",
			LastName: JSON.parse(localStorage.getItem('userProfile'))?.lastName||"",
			Email: JSON.parse(localStorage.getItem('userProfile'))?.email || '',
			SuppliedEmail: JSON.parse(localStorage.getItem('userProfile'))?.email || '',
			Subject: "AppHero"
		};

		embedded_svc.settings.enabledFeatures = ['LiveAgent'];
		embedded_svc.settings.entryFeature = 'LiveAgent';

		embedded_svc.init(
			'https://iapro--prodcopy.sandbox.my.salesforce.com',
			'https://iapro--prodcopy.sandbox.my.site.com/InstitutionalPortal',
			gslbBaseURL,
			'00DUC0000009hc1',
			'AppHero',
			{
				baseLiveAgentContentURL: 'https://c.la2s-core1.sfdc-cehfhs.salesforceliveagent.com/content',
				deploymentId: '5720X0000000HNq',
				buttonId: '573UC0000001A29',
				baseLiveAgentURL: 'https://d.la2s-core1.sfdc-cehfhs.salesforceliveagent.com/chat',
				eswLiveAgentDevName: 'EmbeddedServiceLiveAgent_Parent04IUC00000009o52AA_18d838d1139',
				isOfflineSupportEnabled: false
			}
		);
		// Disable email and supplied email fields after a short delay
		setTimeout(function () {
			var emailField = document.getElementById("embedded_svc_prechat_form_input_email");
			if (emailField) {
				emailField.disabled = true;
			}

			var suppliedEmailField = document.getElementById("embedded_svc_prechat_form_input_suppliedEmail");
			if (suppliedEmailField) {
				suppliedEmailField.disabled = true;
			}
		}, 1000); // 1000 milliseconds (1 second) delay
		// Disable email and supplied email fields
		document.addEventListener('DOMContentLoaded', function () {
			var emailField = document.getElementById("embedded_svc_prechat_form_input_email");
			if (emailField) {
				emailField.disabled = true;
			}

			var suppliedEmailField = document.getElementById("embedded_svc_prechat_form_input_suppliedEmail");
			if (suppliedEmailField) {
				suppliedEmailField.disabled = true;
			}
		});

	};

	if (!window.embedded_svc) {
		var s = document.createElement('script');
		s.setAttribute('src', 'https://iapro--prodcopy.sandbox.my.salesforce.com/embeddedservice/5.0/esw.min.js');
		s.onload = function () {
			initESW(null);
		};
		document.body.appendChild(s);
	} else {
		initESW('https://service.force.com');
	}
