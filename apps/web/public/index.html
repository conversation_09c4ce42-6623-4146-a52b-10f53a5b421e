<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/logo.png" />
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <link rel="stylesheet" href="%PUBLIC_URL%/index.css" />
  <link rel="preload" href="/fonts/Inter.ttf" as="font" type="font/ttf" crossorigin />

  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1.0">

  <style>
    .osano-cm-button--type_deny {display:none!important;}
    .osano-cm-button--type_denyAll {display:none!important;}
    </style>
    <script>
      window.dataLayer = window.dataLayer ||[];
      function gtag(){dataLayer.push(arguments);}
      gtag('consent','default',{
        'ad_storage':'denied',
        'analytics_storage':'denied',
        'ad_user_data':'denied',
        'ad_personalization':'denied',
         'wait_for_update': 500
      });
      gtag("set", "ads_data_redaction", true);
    </script>
    <script async src="https://cmp.osano.com/16BQRvTGwqLg43AqP/a92e93c7-2bb3-4474-8844-8124f01c4933/osano.js"></script>

  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':

    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],

    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=

    'https://www.googletagmanager.com/gtm.js?id='+"%REACT_APP_GTM_ID%"+dl;f.parentNode.insertBefore(j,f);

    })(window,document,'script','dataLayer','%REACT_APP_GTM_ID%');

    </script>

  <title>AppHero</title>
</head>

<body>
  <script async src="https://snack.expo.dev/embed.js"></script>
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=%REACT_APP_GTM_ID%" height="0" width="0" style="display:none;visibility:hidden"></iframe>
  </noscript>

  <div id="root"></div>
<script type='text/javascript' src='https://service.force.com/embeddedservice/5.0/esw.min.js'></script>

</body>
</html>