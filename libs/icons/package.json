{"name": "@app-hero/native-icons", "version": "1.0.0", "description": "", "private": false, "main": "output/index.esm.js/index.js", "module": "output/index.esm.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prebuild": "rimraf src & rimraf dist", "svgr": "svgr --icon --native --replace-attr-values '#16131B'={props.color} -d src assets --out-dir dist --index-template index-template.js", "build-lib": "rollup -c"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@rollup/plugin-babel": "^5.3.1", "@svgr/cli": "^6.3.1", "rimraf": "^3.0.2", "rollup": "^2.78.1", "rollup-plugin-filesize": "^9.1.2"}, "dependencies": {"react-native-svg": "^12.1.1", "prop-types": "^15.8.1"}, "peerDependencies": {"react": "*", "react-native": "*"}}