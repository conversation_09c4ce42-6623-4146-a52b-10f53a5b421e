import { Auth } from 'aws-amplify'
import { countryCodes } from '../countryList/countryList'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { setAuthCookies } from '@app-hero/shared/src/utils'

export const forgotPasswordAsync = async (userName) => {
  try {
    const res = await Auth.forgotPassword(userName)
    return res
    console.log({ res })
  } catch (error) {
    return error
  }
}

export const resetPasswordAsync = async (username, code, newPassword) => {
  try {
    const res = await Auth.forgotPasswordSubmit(username, code, newPassword)
    return res
  } catch (error) {
    return error
  }
}

export const changePasswordAsync = async (currentPassword, newPassword) => {
  try {
    const user = await Auth.currentAuthenticatedUser()
    const res = await Auth.changePassword(user, currentPassword, newPassword)
    console.log(res)
    return res
  } catch (error) {
    return error
  }
}

export const completeNewPasswordAsync = async (user, newPassword) => {
  try {
    await Auth.completeNewPassword(user, newPassword)
  } catch (error) {
    console.log({ error })
  }
}

export const emailSignUp = async (email, password, userDetails) => {
  try {
    const response = await Auth.signUp({
      username: email,
      password: password,
      attributes: userDetails,
    })
  } catch (error) {
    return error
  }
}
export const confirmSignUp = async (username, code, password) => {
  try {
    const res = await Auth.confirmSignUp(username, code)

    if (res) {
      try {
        const result = await emailSignIn(username, password)
        return result
      } catch (error) {
        return error
      }
    }
  } catch (error) {
    console.log('error', error)
    return error
  }
}

export const emailSignIn = async (email, password) => {
  try {
    const signInResponse = await Auth.signIn({
      username: email,
      password,
    })
    if (signInResponse?.attributes) {
      const contact = countryCodes
        .filter((item) =>
          signInResponse.attributes?.phone_number?.includes(item.dial_code),
        )
        .map((item) => ({
          phoneNo: signInResponse.attributes?.phone_number.replace(
            item.dial_code,
            '',
          ),
          dialCode: item.dial_code,
        }))

      const userData = {
        email: signInResponse.attributes?.email || '',
        userID: signInResponse.username || '',
        accessToken: signInResponse.signInUserSession.accessToken.jwtToken,
      }

      const tokens = {
        accessToken: signInResponse.signInUserSession.accessToken.jwtToken,
        idToken: signInResponse.signInUserSession.idToken.jwtToken,
        refreshToken: signInResponse.signInUserSession.refreshToken.token,
      }
      setAuthCookies(tokens, {
        days: 30,
        domain: window.location.hostname,
      })
      return userData
    }
  } catch (error) {
    return error
  }
}

export const federatedSignIn = async (providerType) => {
  await Auth.federatedSignIn({ provider: providerType }).catch((err) => {})
}

export const logOut = async () => {
  await AsyncStorage.removeItem('userProfile')
  await Auth.signOut({ global: true }).catch((err) => {
    return { error: err.message }
  })
}
