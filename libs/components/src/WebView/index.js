import { View } from 'react-native'
import React from 'react'
import { WebView as WebViewComponent } from 'react-native-webview'
import { ActivityIndicator } from 'react-native-web'
import { useTheme } from '@libs/theme'

const WebView = ({ url }) => {
  const { colors } = useTheme()
  const webViewRef = React.useRef()
  return (
    <WebViewComponent
      style={{
        flex: 1,
      }}
      startInLoadingState
      source={{
        uri: url,
      }}
      onContentProcessDidTerminate={() => webViewRef.current.reload()}
      renderLoading={() => (
        <View style={styles.indicatorContain}>
          <ActivityIndicator color={colors.primary} size="large" />
        </View>
      )}
    />
  )
}

export default WebView
