# GUS-App-Hero

App Hero is a Salesforce application designed for monitoring students' university applications. It empowers students to view available programs offered by institutions and facilitates enrollment.

Access to the application is limited to invited users or those who have self-registered.

# Modules

**Login Screen and SignUp Screen**: user can able to login or create new account via email and linkedin.

**Find Program**: user able to see the list program offered by universities and able to view it by programs and by institution. They can able to filter based on the data available on filter.

**Program Details Screen**: user can able to see the Details of particular program.

**Compare Program Screen**: user can able to see the difference between the list of program which are sleeted by them on the **find program screen**.

**My Application Screen**: Users are capable of viewing the current status of their university applications.

**Application Details Screen**: user can able to see in detail of his application.

# Get Started

To get started with this project, clone the repository to your local machine and install the dependencies using yarn

```
# first clone this project

git clone https://github.com/Incresco/gus-apphero.git

# run yarn command from root folder
yarn

```

## Folder Structure

```
    │
    ├── apps
    │   ├── desktop
    │   ├── mobile
    │   └── web
    │
    ├── libs
    │   ├── components
    │   ├── icons
    │   └── theme
    │
    ├── packages
    │   ├── shared
    │   └── src
    │
    │
    ├── package.json
    ├── .gitignore
    └── README.md
```

# Deployment Link

### QA Environment

1. [CloudFront](https://dd6zaohbrvml5.cloudfront.net/)
2. [s3Bucket](https://s3.console.aws.amazon.com/s3/buckets/gus-apphero-dev?region=ap-south-1&tab=objects)

# Figma

1. [V0.2](<https://www.figma.com/file/JBhOTqSgTkSKLT4aFBeoXV/AppHero-(Student-View)-V0.2?type=design&node-id=6278-60696&mode=design&t=VNgzcXOXd2VMuFpq-0>)

2. [V0.4](<https://www.figma.com/file/1wLBEr1qfmpZU34UbIoKJc/AppHero-(Student-View)-V0.4?type=design&node-id=5259-66700&mode=design&t=NU9NRPukiR25GaQE-0>)

3. [V0.6](<https://www.figma.com/file/ugBOql8nwaSRn3lIeg98Gp/AppHero-(Student-View)-V0.6?type=design&node-id=8056-84825&mode=design&t=INHUQ4HYUaeGm4j7-0>)

## Commands

| Command        | Action                                  |
| :------------- | :-------------------------------------- |
| `yarn`         | install the required dependencies       |
| `yarn web`     | run the project in web `localhost:3000` |
| `yarn android` | run the application android emulator    |
| `yarn ios`     | run the application ios emulator        |
| `yarn start`   | start the metro bundle                  |
