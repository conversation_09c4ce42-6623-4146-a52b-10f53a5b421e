import { View, StyleSheet } from 'react-native'
import React from 'react'
import { Text } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { useTheme } from '@libs/theme'

const BrandInformationCard = (props) => {
  const { items } = props
  const totalItem = items?.length - 1
  return (
    <View style={styles.container}>
      <View style={styles.subContainer}>
        {items.map((item, index) => {
          return <ContentCard {...item} index={index} totalItem={totalItem} />
        })}
      </View>
    </View>
  )
}

const ContentCard = ({
  title = '',
  content = '',
  iconName = '',
  index,
  totalItem,
}) => {
  const { colors } = useTheme()
  return (
    <View
      style={[
        styles.contentContainer,
        { marginBottom: totalItem === index ? 0 : 30 },
      ]}
      key={index}
    >
      <View style={styles.icon}>
        <Icon name={iconName} color={colors.white} />
      </View>
      <View style={styles.subContentContainer}>
        <Text
          variant="heading6"
          color={colors.white}
          style={{ marginBottom: 20 }}
        >
          {title}
        </Text>
        <Text variant="display4" color={colors.white}>
          {content}
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 35,
    backgroundColor: 'rgba(256,256,256,0.3)',
    borderRadius: 20,
    marginBottom: 40,
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  icon: {
    backgroundColor: 'rgba(256,256,256,0.2)',
    borderRadius: 15,
    height: 66,
    width: 66,
    marginRight: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subContainer: {
    flex: 1,
    borderRadius: 20,
  },
  subContentContainer: {
    flexDirection: 'column',
    flex: 1,
  },
})

export default BrandInformationCard
