import { useQueryClient } from '@tanstack/react-query'
import React, { useRef, useState, useEffect } from 'react'
import {
  View,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native'
import { DropDown, Text } from '@libs/components'
import { useTheme } from '@libs/theme'
import { t } from 'i18next'
import CustomWebDatePicker from './CustomWebDatePicker'
import { DocumentList } from './documentList'
import { createVisaApplication, signedurl } from '../../api'
import { handleNavigation } from '../../utils/function'
import { CustomFilePicker } from './customFilePicker'

export const VisaDocumentForm = ({ data, visaDocuments }) => {
  const [dropdownTop, setDropdownTop] = useState(0)
  const [dropdownLeft, setDropdownLeft] = useState(0)
  const [dropdownWidth, setDropDownWidth] = useState(0)
  const [metadata, setMetadata] = useState({
    visaRequired: null,
    applicationStatus: '',
    applicationDate: null,
    interviewDate: null,
    applicationRefNumber: '',
    visaDocument: null,
    arrivalDate: null,
    id: null,
  })
  const [originalMetadata, setOriginalMetadata] = useState(null)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const { width } = useWindowDimensions()

  const queryClient = useQueryClient()

  // Prefill form with existing data if available
  useEffect(() => {
    if (
      data?.Visa_Application__r?.records &&
      data?.Visa_Application__r?.records.length > 0
    ) {
      const visaData = data?.Visa_Application__r?.records[0]

      if (visaData?.Id) {
        setIsSubmitted(true)
      }

      // Find the matching status option
      const matchingStatus = VISA_STATUS_OPTIONS.find(
        (option) => option.value === visaData.Visa_Application_Status__c,
      )

      setSelectedApplicationStatus(matchingStatus || {})

      const initialMetadata = {
        visaRequired: visaData.Visa_Required__c,
        applicationStatus: visaData.Visa_Application_Status__c || '',
        applicationDate: visaData.Visa_Application_Date__c || null,
        interviewDate: visaData.Visa_Interview_Date__c || null,
        applicationRefNumber:
          visaData.Visa_Application_Reference_Number__c || null,
        arrivalDate: visaData.Arrival_Date__c || null,
        id: visaData.Id || null,
      }

      setMetadata(initialMetadata)
      setOriginalMetadata(JSON.parse(JSON.stringify(initialMetadata)))
      setHasUnsavedChanges(false)
    } else {
      // If no existing data, set original metadata to initial empty state
      setOriginalMetadata(JSON.parse(JSON.stringify(metadata)))
    }
  }, [data?.Visa_Application__r])

  useEffect(() => {
    let timer
    if (showSuccessMessage) {
      timer = setTimeout(() => {
        setShowSuccessMessage(false)
      }, 5000) // 5 seconds
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [showSuccessMessage])

  // Check for unsaved changes whenever metadata changes
  useEffect(() => {
    if (originalMetadata) {
      // Create copies of metadata for comparison, but handle documents specially
      const metadataCopy = { ...metadata }
      const originalMetadataCopy = { ...originalMetadata }

      // Don't include documents in the basic comparison since they're tracked separately
      delete metadataCopy.visaDocument
      delete originalMetadataCopy.visaDocument

      // Check if basic metadata fields have changed
      const basicFieldsChanged =
        JSON.stringify(metadataCopy) !== JSON.stringify(originalMetadataCopy)

      // Check if documents have changed - any new documents in metadata.visaDocument means changes
      const documentsChanged =
        metadata.visaDocument &&
        Array.isArray(metadata.visaDocument) &&
        metadata.visaDocument.length > 0

      // Set unsaved changes flag if either basic fields or documents have changed
      setHasUnsavedChanges(basicFieldsChanged || documentsChanged)
    }
  }, [metadata, originalMetadata])

  const VISA_STATUS_OPTIONS = [
    {
      value: 'Not Applied – Conditional offer holder',
      label: 'Not Applied – Conditional offer holder',
      code: 'NO_FIELD',
    },
    {
      value: 'Not Applied – Unconditional offer holder',
      label: 'Not Applied – Unconditional offer holder',
      code: 'NO_FIELD',
    },
    {
      value: 'Applied – Awaiting Embassy Interview',
      label: 'Applied – Awaiting Embassy Interview',
      code: 'APPLIED',
    },
    {
      value: 'Applied – Received Embassy Interview',
      label: 'Applied – Received Embassy Interview',
      code: 'INTERVIEW_ATTENDED',
    },
    {
      value: 'Applied – Attended Embassy Interview',
      label: 'Applied – Attended Embassy Interview',
      code: 'INTERVIEW_ATTENDED',
    },
    {
      value: 'Applied – More information requested by the Embassy',
      label: 'Applied – More information requested by the Embassy',
      code: 'INTERVIEW_ATTENDED',
    },
    {
      value: 'Visa Refusal – Appealing Decision',
      label: 'Visa Refusal – Appealing Decision',
      code: 'INTERVIEW_ATTENDED',
    },
    {
      value: 'Visa Refusal – Not Appealing Decision',
      label: 'Visa Refusal – Not Appealing Decision',
      code: 'INTERVIEW_ATTENDED',
    },
    { value: 'Visa Granted', label: 'Visa Granted', code: 'VISA_GRANTED' },
    { value: 'Withdrawn', label: 'Withdrawn', code: 'NO_FIELD' },
  ]

  const handleSubmit = async () => {
    const transformedMetadata = {
      opportunityId: data?.Id,
      applicationFormId: data?.ApplicationFormId__c,
      email: data?.PK || data?.Account.PersonEmail,
      visaUpdatePayload: { Visa_Required__c: metadata.visaRequired },
      BusinessUnitFilter__c: data?.BusinessUnitFilter__c,
      documentDetails: [],
      ...(isSubmitted ? { Id: metadata.id } : {}),
    }

    if (metadata.visaRequired) {
      transformedMetadata.visaUpdatePayload.Visa_Application_Status__c =
        metadata.applicationStatus
    }

    if (selectedApplicationStatus.code === 'APPLIED' && metadata.visaRequired) {
      transformedMetadata.visaUpdatePayload = {
        ...transformedMetadata.visaUpdatePayload,
        Visa_Application_Reference_Number__c: metadata.applicationRefNumber,
        Visa_Application_Date__c: metadata.applicationDate,
      }
    } else if (
      selectedApplicationStatus.code === 'INTERVIEW_ATTENDED' &&
      metadata.visaRequired
    ) {
      transformedMetadata.visaUpdatePayload = {
        ...transformedMetadata.visaUpdatePayload,
        Visa_Application_Reference_Number__c: metadata.applicationRefNumber,
        Visa_Application_Date__c: metadata.applicationDate,
        Visa_Interview_Date__c: metadata.interviewDate,
      }
    } else if (
      selectedApplicationStatus.code === 'VISA_GRANTED' &&
      metadata.visaRequired
    ) {
      transformedMetadata.visaUpdatePayload = {
        ...transformedMetadata.visaUpdatePayload,
        Visa_Application_Date__c: metadata.applicationDate,
        Visa_Interview_Date__c: metadata.interviewDate,
        Arrival_Date__c: metadata.arrivalDate,
        Visa_Application_Reference_Number__c: metadata.applicationRefNumber,
      }

      if (metadata?.visaDocument?.length > 0) {
        transformedMetadata.documentDetails = metadata?.visaDocument?.map(
          (document) => ({
            base64: document.file,
            contentType: document.contentType,
            documentType: 'Visa',
            fileName: document.name,
          }),
        )
      }
    }

    setIsLoading(true)

    const saveResponse = await createVisaApplication(transformedMetadata)

    if (saveResponse?.success) {
      // Create updated metadata with ID first
      const updatedMetadata = {
        ...metadata,
        id: saveResponse?.visaApplicationId || metadata.id, // Use existing ID as fallback
        // Don't reset visaDocument yet
      }

      if (saveResponse?.visaApplicationId) {
        setIsSubmitted(true)
      }

      if (saveResponse?.visaDocuments?.length > 0) {
        // Add newly saved documents to the visaDocuments array
        saveResponse?.visaDocuments?.forEach((document) => {
          visaDocuments.push(document)
        })

        // Now reset the document state
        updatedMetadata.visaDocument = null

        await queryClient.setQueryData(
          [`getDocumentsByOpportunityId-${data?.Id}`],
          (prev) =>
            prev
              ? [...prev, ...saveResponse.visaDocuments]
              : saveResponse.visaDocuments,
        )
      }

      // Set all states in the correct order
      setMetadata(updatedMetadata)
      setOriginalMetadata(JSON.parse(JSON.stringify(updatedMetadata)))
      setHasUnsavedChanges(false)
      setIsLoading(false)
      setShowSuccessMessage(true)

      // Rest of your queryClient updates...

      global.showToast('Successfully saved your visa data.', {
        type: 'success',
      })
    } else {
      setIsLoading(false)
      global.showToast('Unable to update visa data. Please try again later.', {
        type: 'error',
      })
    }
  }

  const [fileUploadError, setFileUploadError] = useState(null)

  const { colors } = useTheme()

  const dropDownRef = useRef()

  const toggleDropdown = () => {
    dropDownRef?.current?.measure((_fx, _fy, _w, _h, _px, py) => {
      setDropdownTop(py + 50)
      setDropdownLeft(_px - 10)
      setDropDownWidth(_w)
    })
  }

  const [selectedApplicationStatus, setSelectedApplicationStatus] = useState({})

  const handleMetadataChange = (field, value) => {
    // Define which fields should be stored as arrays
    const arrayFields = ['visaDocument']

    setMetadata((prev) => {
      let newValue = value

      // Convert to array if needed
      if (arrayFields.includes(field)) {
        // Get existing array or create empty array if it doesn't exist
        const existingValues =
          Array.isArray(prev[field]) && prev[field].length > 0
            ? prev[field]
            : []

        // Add new value(s) to existing values
        if (Array.isArray(value)) {
          newValue = [...existingValues, ...value]
        } else {
          newValue = [...existingValues, value]
        }
      }

      return {
        ...prev,
        [field]: newValue,
      }
    })
  }

  // Check if visa is required to show status field
  const isVisaRequired = metadata.visaRequired === true

  // Check if visa granted to show visa number and document
  const isVisaGranted = selectedApplicationStatus.code === 'VISA_GRANTED'

  // Form fields that appear based on visa requirement and status
  const renderFormFields = () => (
    <View>
      {/* Common fields for all visa required statuses */}

      {isVisaRequired &&
        selectedApplicationStatus.code &&
        selectedApplicationStatus.code !== 'NO_FIELD' && (
          <>
            {selectedApplicationStatus.code === 'APPLIED' && (
              <CustomWebDatePicker
                label="Application Date"
                value={metadata.applicationDate}
                onChange={(date) =>
                  handleMetadataChange('applicationDate', date)
                }
                placeholder="Select date"
                required
              />
            )}

            {selectedApplicationStatus.code === 'INTERVIEW_ATTENDED' ||
            selectedApplicationStatus.code === 'VISA_GRANTED' ? (
              <View
                style={{ flexDirection: 'row', gap: 16, overflow: 'hidden' }}
              >
                <CustomWebDatePicker
                  label="Application Date"
                  value={metadata.applicationDate}
                  onChange={(date) =>
                    handleMetadataChange('applicationDate', date)
                  }
                  placeholder="Select date"
                  required
                />

                <CustomWebDatePicker
                  label="Interview Date"
                  value={metadata.interviewDate}
                  onChange={(date) =>
                    handleMetadataChange('interviewDate', date)
                  }
                  placeholder="Select date"
                  required
                />
              </View>
            ) : null}

            {selectedApplicationStatus.code === 'VISA_GRANTED' ? (
              <View
                style={{ flexDirection: 'row', gap: 16, overflow: 'hidden' }}
              >
                <CustomWebDatePicker
                  label="Arrival Date"
                  value={metadata.arrivalDate}
                  onChange={(date) => handleMetadataChange('arrivalDate', date)}
                  placeholder="Select date"
                  required
                />

                <MetadataField
                  label="Visa Application Reference Number"
                  value={metadata.applicationRefNumber}
                  onChange={(text) =>
                    handleMetadataChange('applicationRefNumber', text)
                  }
                  colors={colors}
                  placeholder="Enter Visa Application Reference Number"
                />
              </View>
            ) : null}

            {(selectedApplicationStatus.code === 'APPLIED' ||
              selectedApplicationStatus.code === 'INTERVIEW_ATTENDED') && (
              <MetadataField
                label="Visa Application Reference Number"
                value={metadata.applicationRefNumber}
                onChange={(text) =>
                  handleMetadataChange('applicationRefNumber', text)
                }
                colors={colors}
                placeholder="Enter Visa Application Reference Number"
              />
            )}
          </>
        )}

      {/* Fields specific to visa granted status */}
      {isVisaGranted && (
        <>
          <Text style={[styles.label(colors), { marginBottom: 0 }]}>
            Upload Visa <Text style={styles.requiredStar}>*</Text>
          </Text>
          <CustomFilePicker
            handleFileSelect={({ fileJson, error, file }) => {
              if (error) {
                setFileUploadError(error)
              } else {
                setFileUploadError(null)
                handleMetadataChange('visaDocument', {
                  ...fileJson,
                  file,
                  createdAt: new Date().toISOString(),
                })
              }
            }}
            style={{ marginBottom: 12 }}
          />
          {fileUploadError ? (
            <Text
              variant="display4"
              color={colors.onAlert}
              style={{ marginBottom: 12 }}
            >
              {fileUploadError}
            </Text>
          ) : null}

          {visaDocuments?.length > 0 || metadata.visaDocument?.length > 0 ? (
            <View style={{ marginBottom: 6 }}>
              <Text style={styles.label(colors)}>
                Visa Documents <Text style={styles.requiredStar}>*</Text>
              </Text>

              {metadata?.visaDocument
                ?.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                ?.map((document, index) => (
                  <View key={index} style={{ marginTop: 10 }}>
                    <DocumentList
                      index={index}
                      item={document}
                      style={{ fontWeight: '700', fontSize: 16 }}
                      deleteDocument={(item) => {
                        if (item.name) {
                          const updatedVisaDocuments =
                            metadata.visaDocument.filter(
                              (doc) => doc.name !== item.name,
                            ) || []

                          setMetadata({
                            ...metadata,
                            visaDocument: updatedVisaDocuments,
                          })
                        }
                      }}
                    />
                  </View>
                ))}

              {visaDocuments
                ?.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                ?.map((document, index) => (
                  <View key={index} style={{ marginTop: 10 }}>
                    <DocumentList
                      index={index}
                      item={document}
                      onPress={async () => {
                        const resp = await signedurl(document)
                        if (resp.FilePath) {
                          await handleNavigation({
                            url: resp.FilePath,
                            fileName: document?.Name,
                            translation: t,
                          })
                        }
                      }}
                      isUploaded={document?.S3FileName__c}
                      style={{ fontWeight: '700', fontSize: 16 }}
                    />
                  </View>
                ))}
            </View>
          ) : null}
        </>
      )}

      {selectedApplicationStatus.code &&
        selectedApplicationStatus.code !== 'NO_FIELD' && (
          <View
            style={{
              borderTopWidth: 1,
              borderColor: '#00000033',
              marginTop: 14,
              borderStyle: 'dashed',
            }}
          />
        )}
    </View>
  )

  const renderMobileFormFields = () => (
    <View>
      {/* Common fields for all visa required statuses */}

      {isVisaRequired &&
        selectedApplicationStatus.code &&
        selectedApplicationStatus.code !== 'NO_FIELD' && (
          <>
            {selectedApplicationStatus.code === 'APPLIED' && (
              <CustomWebDatePicker
                label="Application Date"
                value={metadata.applicationDate}
                onChange={(date) =>
                  handleMetadataChange('applicationDate', date)
                }
                placeholder="Select date"
                required
              />
            )}

            {selectedApplicationStatus.code === 'INTERVIEW_ATTENDED' ||
            selectedApplicationStatus.code === 'VISA_GRANTED' ? (
              <View>
                <CustomWebDatePicker
                  label="Application Date"
                  value={metadata.applicationDate}
                  onChange={(date) =>
                    handleMetadataChange('applicationDate', date)
                  }
                  placeholder="Select date"
                  required
                />

                <CustomWebDatePicker
                  label="Interview Date"
                  value={metadata.interviewDate}
                  onChange={(date) =>
                    handleMetadataChange('interviewDate', date)
                  }
                  placeholder="Select date"
                  required
                />
              </View>
            ) : null}

            {selectedApplicationStatus.code === 'VISA_GRANTED' ? (
              <View>
                <CustomWebDatePicker
                  label="Arrival Date"
                  value={metadata.arrivalDate}
                  onChange={(date) => handleMetadataChange('arrivalDate', date)}
                  placeholder="Select date"
                  required
                />

                <MetadataField
                  label="Visa Application Reference Number"
                  value={metadata.applicationRefNumber}
                  onChange={(text) =>
                    handleMetadataChange('applicationRefNumber', text)
                  }
                  colors={colors}
                  placeholder="Enter Visa Application Reference Number"
                />
              </View>
            ) : null}

            {(selectedApplicationStatus.code === 'APPLIED' ||
              selectedApplicationStatus.code === 'INTERVIEW_ATTENDED') && (
              <MetadataField
                label="Visa Application Reference Number"
                value={metadata.applicationRefNumber}
                onChange={(text) =>
                  handleMetadataChange('applicationRefNumber', text)
                }
                colors={colors}
                placeholder="Enter Visa Application Reference Number"
              />
            )}
          </>
        )}

      {/* Fields specific to visa granted status */}
      {isVisaGranted && (
        <>
          <Text style={[styles.label(colors), { marginBottom: 0 }]}>
            Upload Visa <Text style={styles.requiredStar}>*</Text>
          </Text>
          <CustomFilePicker
            handleFileSelect={({ fileJson, error, file }) => {
              if (error) {
                setFileUploadError(error)
              } else {
                setFileUploadError(null)
                handleMetadataChange('visaDocument', {
                  ...fileJson,
                  file,
                  createdAt: new Date().toISOString(),
                })
              }
            }}
            style={{ marginBottom: 12 }}
          />
          {fileUploadError ? (
            <Text
              variant="display4"
              color={colors.onAlert}
              style={{ marginBottom: 12 }}
            >
              {fileUploadError}
            </Text>
          ) : null}

          {visaDocuments?.length > 0 || metadata.visaDocument?.length > 0 ? (
            <View style={{ marginBottom: 6 }}>
              <Text style={styles.label(colors)}>
                Visa Documents <Text style={styles.requiredStar}>*</Text>
              </Text>

              {metadata?.visaDocument
                ?.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                ?.map((document, index) => (
                  <View key={index} style={{ marginTop: 10 }}>
                    <DocumentList
                      index={index}
                      item={document}
                      style={{ fontWeight: '700', fontSize: 16 }}
                      deleteDocument={(item) => {
                        if (item.name) {
                          const updatedVisaDocuments =
                            metadata.visaDocument.filter(
                              (doc) => doc.name !== item.name,
                            ) || []

                          setMetadata({
                            ...metadata,
                            visaDocument:
                              updatedVisaDocuments.length > 0
                                ? updatedVisaDocuments
                                : null,
                          })
                        }
                      }}
                    />
                  </View>
                ))}

              {visaDocuments
                ?.sort(
                  (a, b) => new Date(b.CreatedDate) - new Date(a.CreatedDate),
                )
                ?.map((document, index) => (
                  <View key={index} style={{ marginTop: 10 }}>
                    <DocumentList
                      index={index}
                      item={document}
                      onPress={async () => {
                        const resp = await signedurl(document)
                        if (resp.FilePath) {
                          await handleNavigation({
                            url: resp.FilePath,
                            fileName: document?.Name,
                            translation: t,
                          })
                        }
                      }}
                      isUploaded={document?.S3FileName__c}
                      style={{ fontWeight: '700', fontSize: 16 }}
                    />
                  </View>
                ))}
            </View>
          ) : null}
        </>
      )}

      {selectedApplicationStatus.code &&
        selectedApplicationStatus.code !== 'NO_FIELD' && (
          <View
            style={{
              borderTopWidth: 1,
              borderColor: '#00000033',
              marginTop: 14,
              borderStyle: 'dashed',
            }}
          />
        )}
    </View>
  )

  // Validation function to check required fields
  const validateForm = () => {
    // First check if visa required selection is made
    if (metadata.visaRequired === null) {
      return false
    }

    // If visa is not required, no further validation needed
    if (!isVisaRequired) {
      return true
    }

    // If visa is required, application status must be selected
    if (!selectedApplicationStatus.value) {
      return false
    }

    // Additional validations based on application status code
    if (selectedApplicationStatus.code === 'APPLIED') {
      // For APPLIED status, application date is required
      if (!metadata.applicationDate) {
        return false
      }
    } else if (selectedApplicationStatus.code === 'INTERVIEW_ATTENDED') {
      // For INTERVIEW_ATTENDED status, both application date and interview date are required
      if (!metadata.applicationDate || !metadata.interviewDate) {
        return false
      }
    } else if (selectedApplicationStatus.code === 'VISA_GRANTED') {
      // For VISA_GRANTED status, application date, interview date, arrival date are required
      if (
        !metadata.applicationDate ||
        !metadata.interviewDate ||
        !metadata.arrivalDate
      ) {
        return false
      }

      // Additionally, either existing visa documents OR new uploaded visa documents are required
      if (
        !visaDocuments?.length &&
        (!Array.isArray(metadata.visaDocument) ||
          metadata.visaDocument.length === 0)
      ) {
        return false
      }
    }

    return true
  }

  const isFormValid = validateForm()

  // Add a simple loading state
  if (!VISA_STATUS_OPTIONS) {
    return (
      <View style={styles.container}>
        <Text>Loading...</Text>
      </View>
    )
  }

  return (
    <View style={styles.container(width)}>
      <Text style={styles.title(colors)}>Visa Application Information</Text>

      {hasUnsavedChanges && (
        <Text style={styles.warningText}>
          Your form contains unsaved information. Please click{' '}
          {isSubmitted ? "'Update'" : "'Submit'"} to preserve your data.
        </Text>
      )}
      {!hasUnsavedChanges && showSuccessMessage && (
        <Text style={styles.successText}>All changes have been saved.</Text>
      )}

      {/* Visa Required Question with Radio Buttons */}
      <View style={styles.fieldContainer}>
        <Text style={styles.label(colors)}>
          Is Visa Required for this application?{' '}
          <Text style={styles.requiredStar}>*</Text>
        </Text>
        <View style={styles.radioGroup}>
          <TouchableOpacity
            style={styles.radioOption}
            onPress={() => {
              handleMetadataChange('visaRequired', true)
            }}
          >
            <View
              style={[
                styles.radioButton(colors),
                metadata.visaRequired === true &&
                  styles.radioButtonSelected(colors),
              ]}
            >
              {metadata.visaRequired === true && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
            <Text style={styles.radioLabel}>Yes</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.radioOption}
            onPress={() => {
              handleMetadataChange('visaRequired', false)
            }}
          >
            <View
              style={[
                styles.radioButton(colors),
                metadata.visaRequired === false &&
                  styles.radioButtonSelected(colors),
              ]}
            >
              {metadata.visaRequired === false && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
            <Text style={styles.radioLabel}>No</Text>
          </TouchableOpacity>
        </View>
      </View>

      {metadata.visaRequired === true && (
        <View ref={dropDownRef} style={{ marginTop: 12 }}>
          <Text style={[styles.label(colors), { marginBottom: 4 }]}>
            Visa Application Status <Text style={styles.requiredStar}>*</Text>
          </Text>
          <DropDown
            items={VISA_STATUS_OPTIONS}
            toggleDropdown={toggleDropdown}
            position={{ left: dropdownLeft + 10, top: dropdownTop + 30 }}
            dropdownWidth={dropdownWidth}
            onPress={(selectedOption) => {
              setSelectedApplicationStatus(selectedOption)
              handleMetadataChange('applicationStatus', selectedOption.value)
            }}
            haveUnderLine
            label="Select Application Status"
            dropdownHeight={300}
            style={{
              margin: 0,
              marginTop: 8,
              marginBottom: 12,
            }}
            labelStyle={{ padding: 100 }}
            placeHolderColor={colors.fieldBorder}
            value={selectedApplicationStatus}
          />
        </View>
      )}

      {metadata.visaRequired === true && (
        <View
          style={{
            borderTopWidth: 1,
            borderColor: '#00000033',
            marginTop: 10,
            borderStyle: 'dashed',
            marginBottom: 8,
          }}
        />
      )}

      {/* Display form fields based on visa requirement */}
      {metadata.visaRequired === true && (
        <View style={styles.metadataContainer}>
          {width < 768 ? renderMobileFormFields() : renderFormFields()}
        </View>
      )}

      {/* Submit Button */}
      <View style={styles.buttonContainer(width)}>
        <TouchableOpacity
          style={[
            styles.submitButton(colors),
            (!isFormValid || isLoading || !hasUnsavedChanges) &&
              styles.disabledButton(colors),
          ]}
          disabled={!isFormValid || isLoading || !hasUnsavedChanges}
          onPress={() => {
            handleSubmit()
          }}
        >
          {isLoading ? (
            <Text style={styles.submitButtonText(width)}>
              {isSubmitted ? 'Updating...' : 'Submitting...'}
            </Text>
          ) : (
            <Text style={styles.submitButtonText(width)}>
              {isSubmitted ? 'UPDATE' : 'SUBMIT'}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  )
}

// Helper component for metadata text fields
const MetadataField = ({
  label,
  value,
  onChange,
  multiline = false,
  required = false,
  placeholder,
  colors,
}) => (
  <View style={[styles.fieldContainer, { marginTop: 3 }]}>
    <Text style={[styles.label(colors), { marginBottom: 12 }]}>
      {label} {required && <Text style={styles.requiredStar}>*</Text>}
    </Text>
    <View
      style={[
        styles.inputContainer(colors),
        multiline && styles.multilineInput,
      ]}
    >
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder || `Enter ${label.toLowerCase()}`}
        style={{
          width: '100%',
          border: 'none',
          outline: 'none',
          padding: '8px',
          fontSize: '14px',
          color: value ? colors.textPrimary : '#A1A1AA',
          minHeight: multiline ? '80px' : 'auto',
        }}
      />
    </View>
  </View>
)

const styles = StyleSheet.create({
  container: (width) => ({
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 2,
    padding: width < 1024 ? 24 : 0,
  }),
  title: (colors) => ({
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
    color: colors.textPrimary,
  }),
  statusSelector: {
    marginBottom: 20,
  },
  metadataContainer: {
    marginTop: 8,
  },
  label: (colors) => ({
    marginBottom: 8,
    fontWeight: '400',
    color: colors.textSecondary,
    fontSize: 14,
  }),
  requiredStar: {
    color: '#D72C2C',
    fontWeight: '800',
  },
  fieldContainer: {
    marginBottom: 8,
    flex: 1,
  },
  inputContainer: (colors) => ({
    borderWidth: 1,
    borderColor: colors.fieldBorder,
    borderRadius: 4,
    overflow: 'hidden',
    padding: 6,
  }),
  multilineInput: {
    minHeight: 80,
  },
  badge: {
    alignSelf: 'flex-start',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginVertical: 8,
  },
  badgeText: {
    fontWeight: '600',
    fontSize: 12,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyStateText: {
    color: '#64748B',
    marginTop: 12,
    textAlign: 'center',
  },
  buttonContainer: (width) => ({
    marginTop: 24,
    alignItems: width < 768 ? '' : 'flex-end',
    width: width < 768 ? '100%' : 'auto',
  }),
  submitButton: (colors) => ({
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 50,
    borderRadius: 4,
    alignItems: 'flex-end',
  }),
  disabledButton: (colors) => ({
    backgroundColor: colors.primary,
    opacity: 0.6,
  }),
  submitButtonText: (width) => ({
    color: '#ffff',
    fontSize: 14,
    fontWeight: '700',
    letterSpacing: 0.2,
    width: width < 768 ? '100%' : 'auto',
    textAlign: width < 768 ? 'center' : 'auto',
  }),
  radioGroup: {
    flexDirection: 'row',
    marginTop: 8,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  radioButton: (colors) => ({
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.primaryPlaceHolder,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  }),
  radioButtonSelected: (colors) => ({
    borderColor: colors.primaryPlaceHolder,
  }),
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#0066ff',
  },
  radioLabel: {
    fontSize: 14,
    color: '#333',
  },
  visaDocumentContainer: {
    marginTop: 16,
  },
  warningBanner: {
    backgroundColor: '#FFD700',
    padding: 12,
    borderRadius: 4,
    marginBottom: 16,
  },
  warningText: {
    color: '#D72C2C',
    fontWeight: '700',
    fontSize: 14,
    marginBottom: 24,
  },
  successBanner: {
    backgroundColor: '#00FF00',
    padding: 12,
    borderRadius: 4,
    marginBottom: 16,
  },
  successText: {
    color: '#64748B',
    fontWeight: '700',
    fontSize: 14,
    marginBottom: 24,
  },
})
