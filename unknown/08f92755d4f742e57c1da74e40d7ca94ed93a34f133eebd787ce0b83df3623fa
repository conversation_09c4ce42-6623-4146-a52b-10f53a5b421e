import React from 'react'
import { View, StyleSheet, TouchableOpacity } from 'react-native'
import { Text } from '@libs/components'
import { useNavigation } from '@react-navigation/native'
import { useTheme } from '@libs/theme'
import { useTranslation } from 'react-i18next'

const HelpAndSupportLink = ({ style }) => {
  const navigation = useNavigation()
  const { colors } = useTheme()
  const { t } = useTranslation()

  const handlePress = () => {
    // Navigate to the help and support screen
    navigation.navigate('auth-help-support', {
      previousRouteName: window.location.pathname,
    })
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity onPress={handlePress}>
        <Text variant="display4" style={styles.linkText} color={colors.primary}>
          {t('AUTH.HELP_SUPPORT')}
        </Text>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: 8,
  },
  linkText: {
    fontWeight: 700,
    textDecorationLine: 'underline',
  },
})

export default HelpAndSupportLink
