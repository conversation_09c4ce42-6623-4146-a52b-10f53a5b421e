export const GET_NOTIFICATIONS_QUERY = `
query getInAppNotificationByEmail($email: String!, $filters: NotificationFilterInput, $limit: Int, $offset: Int) {
  getInAppNotificationByEmail(email: $email, filters: $filters, limit: $limit, offset: $offset) {
    total
    unreadCount
    items {
      createdAt
      email
      event
      message
      messageDetails {
        agentAccountName
        messageId
        opportunityId
        taskId
      }
      readStatus
      type
      updatedAt
    }
  }
}
`

export const GET_SPOTLIGHT_NOTIFICATIONS_QUERY = `
query getInAppNotificationByEmail($email: String!, $filters: NotificationFilterInput) {
  getInAppNotificationByEmail(email: $email, filters: $filters) {
    total
    unreadCount
    items {
      createdAt
      email
      event
      message
      messageDetails {
        agentAccountName
        messageId
        opportunityId
        taskId
      }
      readStatus
      type
      updatedAt
    }
  }
}
`

export const SUBSCRIPTION_QUERY = `
subscription OnNewNotification($email: String) {
    onNewNotification(email: $email) {
    createdAt
    email
    event
    message
    readStatus
    updatedAt
    type
    messageDetails {
      agentA<PERSON>unt<PERSON>ame
      messageId
      opportunityId
      taskId
      taskClosedBy
    }
  }
}
`
