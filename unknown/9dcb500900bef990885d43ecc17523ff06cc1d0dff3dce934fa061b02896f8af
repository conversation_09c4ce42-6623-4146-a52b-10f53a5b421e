{"name": "web", "version": "0.1.0", "private": true, "dependencies": {"react": "18.2.0", "react-native-web": "0.18.12", "react-dom": "18.2.0", "react-scripts": "5.0.1", "web-vitals": "2.1.4", "customize-cra": "1.0.0"}, "devDependencies": {"@babel/preset-env": "7.22.6", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "babel-plugin-react-native-web": "0.18.12", "react-app-rewired": "2.2.1"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"preset": "react-native-web"}, "plugins": [["@babel/plugin-proposal-class-properties", {"loose": true}]]}