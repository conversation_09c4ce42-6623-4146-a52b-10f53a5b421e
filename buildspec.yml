version: 0.2

env:
  variables:
    S3_BUCKET_DEV: 'gus-apphero-frontend-dev'
    S3_BUCKET_PROD: 'gus-apphero-frontend-prod'
    CLOUDFRONT_ID_DEV: 'E2GR6F6ZBUBPKM'
    CLOUDFRONT_ID_PROD: 'E825543TU1EF8'

phases:
  install:
    runtime-versions:
      nodejs: 16
    commands:
      # Install dependencies needed
      - npm install -g yarn
      - yarn

  pre_build:
    commands:
      # Set environment-specific variables based on stage
      - |
        if [ "$stage" = "prod" ]; then
          export S3_BUCKET=$S3_BUCKET_PROD
          export CLOUDFRONT_ID=$CLOUDFRONT_ID_PROD
          echo "Building for production environment"
          yarn web:build:prod
        else
          export S3_BUCKET=$S3_BUCKET_DEV
          export CLOUDFRONT_ID=$CLOUDFRONT_ID_DEV
          echo "Building for development environment"
          yarn web:build:dev
        fi

  build:
    commands:
      # Copy the content of build folder into appropriate S3 bucket
      - aws s3 sync apps/web/build/ s3://${S3_BUCKET}/
      - aws cloudfront create-invalidation --distribution-id=${CLOUDFRONT_ID} --paths '/*'
