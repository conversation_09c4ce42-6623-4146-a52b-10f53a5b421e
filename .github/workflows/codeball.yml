name: Codeball
on:
  pull_request: {}
  pull_request_review_comment:
    types: [created, edited]

jobs:
  codeball_job:
    runs-on: ubuntu-latest
    name: Codeball
    steps:
      - name: Codeball
        uses: sturdy-dev/codeball-action@v2
        with:
          # For all configuration options see https://github.com/sturdy-dev/codeball-action/blob/v2/action.yml
          approvePullRequests: 'true'
          labelPullRequestsWhenApproved: 'true'
          labelPullRequestsWhenReviewNeeded: 'false'
          failJobsWhenReviewNeeded: 'false'
