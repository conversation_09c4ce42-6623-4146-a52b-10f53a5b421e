name: Build and Deploy

on:
  push:
    branches:
      - main

env:
  S3_BUCKET: gus-apphero-dev
  CLOUDFRONT_DISTRIBUTION_ID: E2DZPSN4TMTGCD
  AWS_ACCESS_KEY_ID: ${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.DEV_AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: ap-south-1

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [web] # replace with the names of your apps
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Install dependencies
        run: |
          cd apps/web
          yarn install --frozen-lockfile
      - name: Build
        run: |
          cd apps/web
          yarn build
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEV_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}
      - name: deploy
        run: |
          cd apps/web
          aws s3 rm s3://${{ env.S3_BUCKET }}/ --recursive
          aws s3 cp build s3://${{ env.S3_BUCKET }} --cache-control 'public, max-age=31104000' --recursive
          aws configure set preview.cloudfront true
          aws cloudfront create-invalidation --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"
